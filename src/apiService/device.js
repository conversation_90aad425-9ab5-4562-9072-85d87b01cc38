import api from '@/api'
import { Get, Post } from '@/common/request'
function getDeviceTree(param) {
    return Get({
        url: api.getOrgInfoTree,
        urlParam: param
    })
}

function getSupplierStationSummaryOne(param = {}) {
    return Get({
        url: api.getSupplierStationSummary,
        urlParam: param
    })
}

function getStatisticStationSummary(params) {
    return Post({
        url: api.statisticStation,
        bodyParam: params
    })
}

function getPageList(params) {
    return Post({
        url: api.supplierStationPage,
        bodyParam: params
    })
}

function getElectricityAndRevenue(params) {
    return Post({
        url: api.statisticsStationDailyChargeAndProfit,
        bodyParam: params
    })
}

function getStatisticsStationProfitRank(params) {
    return Post({
        url: api.statisticsStationProfitRank,
        bodyParam: params
    })
}

function getDeviceAlarm(params) {
    return Post({
        url: api.deviceAlarm,
        bodyParam: params,
    })
}

function getSocialContribution(params) {
    return Post({
        url: api.statisticSocialContributionDegree,
        bodyParam: params
    })
}
// 设备警告开始
function PostDeviceAlarmPage(params) {//列表
    return Post({
        url: api.deviceAlarmPage,
        bodyParam: params
    })
}


function GetDeviceAlarmDetail(param) {//详情
    return Get({
        url: api.deviceAlarmDetail,
        urlParam: param,
    })
}
function PostDeviceAlarmStatisticalSummary(params) {//告警摘要
    return Post({
        url: api.deviceAlarmStatisticalSummary,
        bodyParam: params
    })
}


function PostDeviceAlarmReportAlarm(params) {//上报警告
    return Post({
        url: api.deviceAlarmReportAlarm,
        bodyParam: params
    })
}
function PostDeviceAlarmClearAlarm(params) {//清除警告
    return Post({
        url: api.deviceAlarmClearAlarm,
        urlParam: params
    })
}
// 设备警告结束
//输出方案开始
function PotsQueryAreaList(params) {//地址
    return Post({
        url: api.queryAreaList,
        bodyParam: params
    })
}
function PostCalculateAreaPrice(params) {//地址
    return Post({
        url: api.calculateAreaPrice,
        bodyParam: params ? params : ''
    })
}
function PostGenerateFinancialStatementReport(params) {//地址
    return Post({
        url: api.generateFinancialStatementReport,
        bodyParam: params ? params : ''
    })
}


//输出方案结束

//获取详情的站点信息 第一个头部echarts
function getStationInfoData(params) {
    return Get({
        url: api.getStationInfo,
        urlParam: params
    })
}

//系统效率
function getDischargeAchievingRate(params) {
    return Post({
        url: api.statisticsDischargeAchievingRate,
        bodyParam: params
    })
}

//趋势分析
function getStationPowerTrend(params) {
    return Post({
        url: api.getStationPowerTrend,
        bodyParam: params
    })
}

//获取机柜tab
function getContainerList(params) {
    return Get({
        url: api.getContainerList,
        urlParam: params
    })
}

// 获取机柜信息
function getContainerInfo(params) {
    return Get({
        url: api.getContainerInfo,
        urlParam: params
    })
}

//获取变bms信息
function getBmsInfo(params) {
    return Get({
        url: api.getBmsInfo,
        urlParam: params
    })
}
//获取储能电表数据
function getBmsMeter(params) {
    return Get({
        url: api.getBmsMeter,
        urlParam: params
    })
}

//获取变流器信息
function getPcsInfo(params) {
    return Get({
        url: api.getPcsInfo,
        urlParam: params
    })
}

function getDynamicEnvironmentSystem(params) {
    return Get({
        url: api.getDynamicEnvironmentSystem,
        urlParam: params
    })
}

//获取电芯数据
function getBatteryPackList(params) {
    return Get({
        url: api.getBatteryPackList,
        urlParam: params
    })

}

//获取电芯下面的电芯温度
function getBatteryVoltageRangeAndVariance(params) {
    return Post({
        url: api.getBatteryVoltageRangeAndVariance,
        bodyParam: params
    })
}

//获取电芯压差分析
function getBatteryTempRangeAndVariance(params) {
    return Post({
        url: api.getBatteryTempRangeAndVariance,
        bodyParam: params
    })
}

function statisticsSocAndWorkStatusByDay(params) {
    return Get({
        url: api.statisticsSocAndWorkStatusByDay,
        urlParam: params
    })
}

// function getCurrentOrgInfoTree(){
//     return Get({
//         url:api.getCurrentOrgInfoTree,
//         urlParam:{}
//     })
// }


//登录
function login(params) {
    return Post({
        url: api.login,
        bodyParam: params
    })
}

//验证码
function sendSmsCode(params) {
    return Post({
        url: api.sendSmsCode,
        bodyParam: params
    })
}
function secondAuth(params) {
    return Post({
        url: api.secondAuth,
        bodyParam: params
    })
}

//用户信息
function getCurrentUserDetail() {
    return Post({
        url: api.getCurrentUserDetail
    })
}


function getAgreements() {
    return Get({
        url: `https://devops.mingwork.com/contract.pdf`,
    });
}

//退出登录
function loginOut() {
    return Post({
        url: api.loginOut
    })
}

//历史列表
function historyPage(params) {
    return Post({
        url: api.historyPage,
        bodyParam: params
    })
}

//获取企业管理列表
function getStaffPage(params) {
    return Post({
        url: api.getStaffPage,
        bodyParam: params
    })
}

function getCurrentOrgData() {
    return Post({
        url: api.getCurrentOrgData
    })
}

function addStaff(params) {
    return Post({
        url: api.addStaff,
        bodyParam: params
    })
}

function editModifyStaff(params) {
    return Post({
        url: api.editModifyStaff,
        bodyParam: params
    })
}

function removeStaff(params) {
    return Post({
        url: api.removeStaff,
        bodyParam: params
    })
}

function getSubOrgWithOwnerPage(params) {
    return Post({
        url: api.getSubOrgWithOwnerPage,
        bodyParam: params
    })
}

function createSubOrgWithOwner(params) {
    return Post({
        url: api.createSubOrgWithOwner,
        bodyParam: params
    })
}

function modifyOrgInfo(params) {
    return Post({
        url: api.modifyOrgInfo,
        bodyParam: params
    })
}

function getOrgWebpageSettingByDomain(params) {
    return Get({
        url: api.getOrgWebpageSettingByDomain,
        urlParam: params
    })
}

function bindStation(params) {
    return Post({
        url: api.bindStation,
        bodyParam: params
    })
}

function getStaffNotificationPage() {
    return Post({
        url: api.getStaffNotificationPage,
        bodyParam: {
            current: 1,
            size: 100
        }
    })
}

function readNotification(params) {
    return Post({
        url: api.readNotification,
        urlParam: params
    })
}
function unBindThirdAccount(params) {
    return Post({
        url: api.unBindThirdAccount,
        bodyParam: params
    })
}

function getBindThirdAccount() {
    return Get({
        url: api.getBindThirdAccount,
    })
}

function getStationStaticInfo(params) {
    return Get({
        url: api.getStationStaticInfo,
        urlParam: params
    })
}

function updateStationInfo(params) {
    return Post({
        url: api.updateStationInfo,
        bodyParam: params,
        config: {
            timeout: 300000
        }
    })
}

function getStatisticalCard(params) {
    return Post({
        url: api.getStatisticalCard,
        bodyParam: params
    })
}

// 统计站点24小时充放电量
function statisticsStation24HourCharge(params) {
    return Post({
        url: api.statisticsStation24HourCharge,
        bodyParam: params
    })
}
function dayElectricPriceSegmentRecord(params) {
    return Get({
        url: api.dayElectricPriceSegmentRecord,
        urlParam: params
    })
}


function getOrgWithStationTopology(params) {
    return Get({
        url: api.getOrgWithStationTopology,
        urlParam: params
    })
}

function statisticOrgTotalCapacityAndProfit(params) {
    return Get({
        url: api.statisticOrgTotalCapacityAndProfit,
        urlParam: params
    })
}

function getEmsRunDataLog(params) {
    return Post({
        url: api.getEmsRunDataLog,
        bodyParam: params
    })
}
function exportEmsRunDataLog(params) {
    return Post({
        url: api.exportEmsRunDataLog,
        bodyParam: params,
        config: {
            responseType: "blob",
            timeout: 100000,
        }
    })
}


function getEmsDataColumn(params) {
    return Get({
        url: api.getEmsDataColumn,
        urlParam: params
    })
}
function statisticsStationDateChargeAndNetProfit(params) {
    return Post({
        url: api.statisticsStationDateChargeAndNetProfit,
        bodyParam: params
    })
}

function exportStationDailyChargeDetail(params) {
    return Post({
        url: api.exportStationDailyChargeDetail,
        bodyParam: params,
        config: {
            responseType: "blob",
            timeout: 100000,
        }
    })
}
function exportStationDailyProfitDetail(params) {
    return Post({
        url: api.exportStationDailyProfitDetail,
        bodyParam: params,
        config: {
            responseType: "blob",
            timeout: 100000,
        }
    })
}
function exportStationDateChargeAndNetProfit(params) {
    return Post({
        url: api.exportStationDateChargeAndNetProfit,
        bodyParam: params,
        config: {
            responseType: "blob",
            timeout: 100000,
        }
    })
}

function getInspectionDevicePage(params) {
    return Post({
        url: api.getInspectionDevicePage,
        bodyParam: params
    })
}
function getInspectionLogPage(params) {
    return Post({
        url: api.getInspectionLogPage,
        bodyParam: params
    })
}
function addInspectionLog(params) {
    return Post({
        url: api.addInspectionLog,
        bodyParam: params
    })
}

function getStatisticalSummary(params) {
    return Post({
        url: api.getStatisticalSummary,
        bodyParam: params
    })
}

function getWorkOrderPage(params) {
    return Post({
        url: api.getWorkOrderPage,
        bodyParam: params
    })
}

function getStaffByRole(params) {
    return Post({
        url: api.getStaffByRole,
        bodyParam: params
    })
}

function getWorkOrderDetail(params) {
    return Get({
        url: api.getWorkOrderDetail,
        urlParam: params
    })
}
function updateWorkOrder(params) {
    return Post({
        url: api.updateWorkOrder,
        bodyParam: params
    })
}
function manualCreate(params) {
    return Post({
        url: api.manualCreate,
        bodyParam: params
    })
}
function alarmTransfer(params) {
    return Post({
        url: api.alarmTransfer,
        bodyParam: params
    })
}
function getWorkOrderStatisticalCard(params) {
    return Post({
        url: api.getWorkOrderStatisticalCard,
        bodyParam: params
    })
}

function createWechatBindQrcode(params) {
    return Post({
        url: api.createWechatBindQrcode,
        bodyParam: params,
    });
}
function getDictByType(params) {
    return Get({
        url: api.getDictByType,
        urlParam: params
    })
}
function getSubSupplierWithOwnerTree(params) {
    return Get({
        url: api.getSubSupplierWithOwnerTree,
        urlParam: params
    })
}



export default {
    getDeviceTree,
    getSupplierStationSummaryOne,
    getStatisticStationSummary,
    getPageList,
    getElectricityAndRevenue,
    getStatisticsStationProfitRank,
    getDeviceAlarm,
    getSocialContribution,
    getStationInfoData,
    getDischargeAchievingRate,
    getStationPowerTrend,
    getContainerList,
    getContainerInfo,
    getBmsInfo,
    getPcsInfo,
    getBmsMeter,
    getDynamicEnvironmentSystem,
    getBatteryPackList,
    getBatteryVoltageRangeAndVariance,
    getBatteryTempRangeAndVariance,
    statisticsSocAndWorkStatusByDay,
    PostDeviceAlarmPage,
    GetDeviceAlarmDetail,
    PostDeviceAlarmStatisticalSummary,
    PostDeviceAlarmReportAlarm,
    PostDeviceAlarmClearAlarm,
    PotsQueryAreaList,
    PostCalculateAreaPrice,
    PostGenerateFinancialStatementReport,
    // getCurrentOrgInfoTree,
    login,
    sendSmsCode,
    secondAuth,
    getCurrentUserDetail,
    loginOut,
    getAgreements,
    historyPage,
    getStaffPage,
    getCurrentOrgData,
    addStaff,
    editModifyStaff,
    removeStaff,
    getSubOrgWithOwnerPage,
    createSubOrgWithOwner,
    modifyOrgInfo,
    getOrgWebpageSettingByDomain,
    bindStation,
    getStaffNotificationPage,
    readNotification,
    unBindThirdAccount,
    getBindThirdAccount,
    getStationStaticInfo,
    updateStationInfo,
    getStatisticalCard,
    statisticsStation24HourCharge,
    dayElectricPriceSegmentRecord,
    getOrgWithStationTopology,
    statisticOrgTotalCapacityAndProfit,
    getEmsRunDataLog,
    exportEmsRunDataLog,
    getEmsDataColumn,
    statisticsStationDateChargeAndNetProfit,
    exportStationDailyChargeDetail,
    exportStationDailyProfitDetail,
    exportStationDateChargeAndNetProfit,

    // 巡检
    getInspectionDevicePage,
    getInspectionLogPage,
    addInspectionLog,

    // 工单
    getStatisticalSummary,
    getWorkOrderPage,
    getStaffByRole,
    getWorkOrderDetail,
    updateWorkOrder,
    manualCreate,
    alarmTransfer,
    getWorkOrderStatisticalCard,
    createWechatBindQrcode,
    getDictByType,
    getSubSupplierWithOwnerTree,
}
