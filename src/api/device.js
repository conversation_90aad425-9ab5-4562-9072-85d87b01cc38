export default {
  getSupplierStationSummary: 'station/getSupplierStationSummary',
  statisticStation: 'chargeProfit/statisticStationChargeAndProfitSummary',
  supplierStationPage: 'station/device/page',

  statisticsStationDailyChargeAndProfit: 'chargeProfit/statisticsDailyUsageAndProfit',
  statisticsStationProfitRank: 'chargeProfit/statisticsStationProfitRank',
  deviceAlarm: "deviceAlarm/getStatisticalSummary",
  statisticSocialContributionDegree: 'chargeProfit/statisticSocialContributionDegree',
  getStationInfo: 'station/getStationInfo',
  statisticsDischargeAchievingRate: 'ems/statisticsDischargeAchievingRate',
  getStationPowerTrend: 'ems/getStationPowerTrend',
  getContainerList: 'station/getContainerList',
  getContainerInfo: 'station/getContainerInfo',
  getBmsInfo: 'ems/getBmsInfo',
  getPcsInfo: 'ems/getPcsInfo',
  getBmsMeter: 'ems/getBmsMeter',
  getDynamicEnvironmentSystem: 'ems/getDynamicEnvironmentSystem',
  getBatteryPackList: 'ems/getBatteryPackList',
  getBatteryVoltageRangeAndVariance: 'ems/getBatteryVoltageRangeAndVariance',
  getBatteryTempRangeAndVariance: 'ems/getBatteryTempRangeAndVariance',
  statisticsSocAndWorkStatusByDay: 'ems/statisticsSocAndWorkStatusByDayV2',
  // 设备警告开始
  deviceAlarmPage: "deviceAlarm/page",//列表
  historyPage: 'customer/deviceAlarm/history/page', //历史列表
  deviceAlarmDetail: "deviceAlarm/getDetail",//详情
  deviceAlarmStatisticalSummary: "deviceAlarm/getStatisticalSummary",//获取站点告警摘要
  deviceAlarmReportAlarm: "supplier/deviceAlarm/reportAlarm",//上报警告
  deviceAlarmClearAlarm: "deviceAlarm/clearAlarm",//清除警告
  // 设备警告结束
  //输出方案开始
  queryAreaList: 'calculateElectricPrice/queryAreaList',//地区
  //输出方案结束
  //登录接口
  login: 'skipAuth/smsLogin',
  sendSmsCode: 'skipAuth/sendSmsCode',
  secondAuth: 'skipAuth/secondAuth',
  getCurrentUserDetail: 'organization/getCurrentUserDetail',
  getOrgInfoTree: 'station/getSubSupplierTree',
  loginOut: 'skipAuth/loginOut',
  getStaffPage: 'organization/getStaffPage',
  getCurrentOrgData: 'organization/getCurrentOrgInfoWithOwner',
  addStaff: 'organization/addStaff',
  editModifyStaff: 'organization/modifyStaff',
  removeStaff: 'organization/removeStaff',
  getSubOrgWithOwnerPage: 'organization/getSubOrgWithOwnerPage',
  createSubOrgWithOwner: 'organization/createSubOrgWithOwner',
  modifyOrgInfo: 'organization/modifyOrgInfo',
  getOrgWebpageSettingByDomain: 'skipAuth/getOrgWebpageSettingByDomain',
  bindStation: 'station/bindStation',
  getStaffNotificationPage: 'notification/getStaffNotificationPage',
  readNotification: 'notification/readNotification',
  unBindThirdAccount: "skipAuth/unBindThirdAccount",
  getBindThirdAccount: "skipAuth/getBindThirdAccount",

  bindDevice: 'deviceV2/bindDevice',
  getStationStaticInfo: 'station/getStationStaticInfo',
  updateStationInfo: 'station/updateStationInfo',
  getStatisticalCard: "deviceAlarm/getStatisticalCard",
  statisticsStation24HourCharge: 'chargeProfit/statisticsStation24HourCharge',

  // 
  dayElectricPriceSegmentRecord: "elecPrice/station/dayElectricPriceSegmentRecord",

  getOrgWithStationTopology: 'station/getOrgWithStationTopology',
  statisticOrgTotalCapacityAndProfit: "chargeProfit/statisticOrgTotalCapacityAndProfit",


  //数据统计
  getEmsRunDataLog: "emsData/getEmsRunDataLog",
  exportEmsRunDataLog: 'emsData/exportEmsRunDataLog',
  getEmsDataColumn: 'emsData/getEmsDataColumn',
  statisticsStationDateChargeAndNetProfit: 'chargeProfit/statisticsStationDateChargeAndNetProfit',
  exportStationDailyChargeDetail: 'chargeProfit/exportStationDailyChargeDetail',
  exportStationDailyProfitDetail: 'chargeProfit/exportStationDailyProfitDetail',
  exportStationDateChargeAndNetProfit: 'chargeProfit/exportStationDateChargeAndNetProfit',

  // 巡检

  getInspectionDevicePage: 'station/getInspectionDevicePage',
  getInspectionLogPage: 'station/getInspectionLogPage',
  addInspectionLog: 'station/addInspectionLog',

  // 工单
  getStatisticalSummary: "workOrder/getStatisticalSummary",
  getWorkOrderPage: 'workOrder/page',
  getStaffByRole: "organization/getStaffByRole",
  getWorkOrderDetail: "workOrder/detail",
  updateWorkOrder: "workOrder/updateWorkOrder",
  manualCreate: "workOrder/manualCreate",
  alarmTransfer: 'workOrder/alarmTransfer',
  getWorkOrderStatisticalCard: 'workOrder/getStatisticalCard',
  createWechatBindQrcode: "skipAuth/createWechatBindQrcode",  // 获取二维码
  // 获取字典
  getDictByType: "common/getDictByType",
  getSubSupplierWithOwnerTree:"organization/getSubSupplierWithOwnerTree"
}
