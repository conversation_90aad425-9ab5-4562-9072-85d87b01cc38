<template>
    <div class="certification-test">
        <h2>Certification Component Test</h2>
        
        <div class="test-controls">
            <el-form :model="testForm" label-width="120px">
                <el-form-item label="Current Phone:">
                    <el-input v-model="testForm.currentPhone" placeholder="Enter phone number" />
                </el-form-item>
                <el-form-item label="Current Email:">
                    <el-input v-model="testForm.currentEmail" placeholder="Enter email address" />
                </el-form-item>
            </el-form>
        </div>

        <div class="certification-wrapper">
            <certification
                :currentPhone="testForm.currentPhone"
                :currentEmail="testForm.currentEmail"
                @update:authType="handleAuthTypeUpdate"
                @update:phone="handlePhoneUpdate"
                @update:email="handleEmailUpdate"
                @update:code="handleCodeUpdate"
            />
        </div>

        <div class="output-display">
            <h3>Component Output:</h3>
            <div class="output-item">
                <strong>Auth Type:</strong> {{ outputData.authType }}
            </div>
            <div class="output-item">
                <strong>Phone:</strong> {{ outputData.phone }}
            </div>
            <div class="output-item">
                <strong>Email:</strong> {{ outputData.email }}
            </div>
            <div class="output-item">
                <strong>Code:</strong> {{ outputData.code }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import Certification from '@/views/user/certification.vue'

const testForm = reactive({
    currentPhone: '13800138000',
    currentEmail: '<EMAIL>'
})

const outputData = reactive({
    authType: '',
    phone: '',
    email: '',
    code: ''
})

const handleAuthTypeUpdate = (value) => {
    outputData.authType = value
    console.log('Auth Type updated:', value)
}

const handlePhoneUpdate = (value) => {
    outputData.phone = value
    console.log('Phone updated:', value)
}

const handleEmailUpdate = (value) => {
    outputData.email = value
    console.log('Email updated:', value)
}

const handleCodeUpdate = (value) => {
    outputData.code = value
    console.log('Code updated:', value)
}
</script>

<style lang="less" scoped>
.certification-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.test-controls {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f9f9f9;
}

.certification-wrapper {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.output-display {
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f0f9ff;
}

.output-item {
    margin-bottom: 10px;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    border-left: 4px solid #409eff;
}

h2 {
    color: #303133;
    margin-bottom: 20px;
}

h3 {
    color: #606266;
    margin-bottom: 15px;
}
</style>
