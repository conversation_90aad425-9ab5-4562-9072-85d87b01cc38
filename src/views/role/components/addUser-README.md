# AddUser 组件功能说明

## 概述

`addUser.vue` 组件用于添加和编辑用户，支持完整的用户信息管理功能。该组件已根据最新需求进行了全面升级。

## 新增功能

### 1. 手机号码字段增强
- **+86 标识**：手机号码输入框前自动添加 +86 国家代码标识
- **格式验证**：保持原有的手机号格式验证

### 2. 邮箱地址字段
- **新增字段**：添加邮箱地址输入框
- **格式验证**：使用正则表达式验证邮箱格式
- **必填验证**：邮箱地址为必填项

### 3. 初始密码字段（仅添加用户时显示）
- **密码输入框**：支持密码输入
- **显示/隐藏功能**：点击眼睛图标切换密码显示状态
- **复制功能**：点击复制图标复制密码到剪贴板
- **密码规则**：
  - 长度：6-20 位
  - 字符支持：大写字母(A-Z)、小写字母(a-z)、数字(0-9)、特殊字符(!@#$%)

### 4. 重复检查逻辑

#### 添加用户时
- 检查邮箱地址是否在系统中重复
- 如果重复，提示："该邮箱地址已被使用，请进行更换"

#### 编辑用户时
- 检查手机号码是否被其他用户占用
- 检查邮箱地址是否被其他用户占用
- 如果任一被占用，提示："该手机号码/邮箱地址已被使用，请进行更换"

## 组件 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| data | Object | null | 编辑用户时传入的用户数据 |
| sites | Array | [] | 可见站点数据 |

## 表单字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| realName | String | ✅ | 用户姓名 |
| phone | String | ✅ | 手机号码（带+86前缀） |
| email | String | ✅ | 邮箱地址 |
| initialPassword | String | ✅* | 初始密码（仅添加用户时） |
| permissions | Array | ❌ | 用户权限 |
| resourceScope | String | ✅ | 数据范围 |
| viewableStationIds | Array | ❌ | 可见站点ID列表 |

*注：初始密码仅在添加用户时为必填

## 验证规则

### 手机号验证
```javascript
const phoneReg = /^1[3456789]\d{9}$/
```

### 邮箱验证
```javascript
const email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
```

### 密码验证
```javascript
const newPassword = /^[A-Za-z0-9!@#$%]{6,20}$/
```

## 使用示例

### 添加用户
```vue
<template>
  <add-user 
    ref="addUserRef" 
    :sites="sitesList" 
  />
</template>

<script setup>
import AddUser from '@/views/role/components/addUser.vue'

const addUserRef = ref(null)
const sitesList = ref([...])

const handleSubmit = async () => {
  const isValid = await addUserRef.value.submitRules()
  if (isValid) {
    const formData = addUserRef.value.formState
    // 处理表单数据
    console.log('User data:', formData)
  }
}
</script>
```

### 编辑用户
```vue
<template>
  <add-user 
    ref="addUserRef" 
    :data="editUserData"
    :sites="sitesList" 
  />
</template>

<script setup>
const editUserData = ref({
  staffId: '123',
  realName: 'John Doe',
  phone: '13800138000',
  email: '<EMAIL>',
  permissions: ['emsControl'],
  resourceScope: 'all',
  viewableStationIds: []
})
</script>
```

## 组件方法

### submitRules()
- **返回值**：Promise<boolean>
- **说明**：执行表单验证和重复检查
- **用法**：
```javascript
const isValid = await addUserRef.value.submitRules()
```

### clearValidate()
- **说明**：清空表单验证状态和数据
- **用法**：
```javascript
addUserRef.value.clearValidate()
```

### resetFields()
- **说明**：重置表单字段
- **用法**：
```javascript
addUserRef.value.resetFields()
```

## 密码功能

### 显示/隐藏密码
- 点击眼睛图标切换密码显示状态
- 显示状态时图标为眼睛闭合，隐藏状态时为眼睛睁开

### 复制密码
- 点击复制图标复制密码到剪贴板
- 优先使用现代浏览器的 `navigator.clipboard` API
- 降级使用 `document.execCommand` 方法（已弃用但兼容性好）
- 复制成功后显示提示消息

## 重复检查 API

### 检查邮箱重复
```javascript
const checkEmailDuplicate = async (email, excludeStaffId = null) => {
  // 需要实现的API调用
  // const response = await apiService.checkEmailExists({ email, excludeStaffId })
  // return response.data.exists
}
```

### 检查手机号重复
```javascript
const checkPhoneDuplicate = async (phone, excludeStaffId = null) => {
  // 需要实现的API调用
  // const response = await apiService.checkPhoneExists({ phone, excludeStaffId })
  // return response.data.exists
}
```

## 国际化支持

组件支持中英文国际化，新增的文本键包括：

- `Initial Password`: 初始密码
- `Show Password`: 显示密码
- `Hide Password`: 隐藏密码
- `Copy`: 复制
- `Password copied to clipboard`: 密码已复制到剪贴板
- `Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)`: 密码规则提示
- `This email address is already in use, please change it`: 邮箱重复提示
- `This phone number/email address is already in use, please change it`: 手机号/邮箱重复提示

## 样式定制

### 密码操作按钮样式
```less
.password-actions {
    display: flex;
    gap: 4px;
    
    .ant-btn {
        border: none;
        box-shadow: none;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }
        
        .anticon {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
    }
}
```

## 注意事项

1. **API 集成**：重复检查的 API 接口需要根据实际后端接口进行实现
2. **密码安全**：初始密码应该由系统生成或用户设置，确保安全性
3. **权限控制**：根据用户角色显示不同的权限选项
4. **数据验证**：在提交前进行完整的数据验证
5. **错误处理**：妥善处理网络错误和验证错误

## 测试

可以使用提供的测试页面 `src/views/test/addUser-test.vue` 来测试组件功能：

```bash
# 在路由中添加测试页面
{
  path: '/test/add-user',
  component: () => import('@/views/test/addUser-test.vue')
}
```

测试页面提供了添加用户和编辑用户两种模式的测试功能。
