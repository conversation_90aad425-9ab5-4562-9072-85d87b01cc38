<template>
    <div class="add-user">
        <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            class="add-form"
            autocomplete="off"
            labelAlign="left"
            :label-col="labelCol"
            hideRequiredMark
        >
            <a-form-item :label="$t('user_xingming')" name="realName">
                <a-input
                    v-model:value="formState.realName"
                    :placeholder="$t('placeholder_qingshuruxingming')"
                    :maxLength="12"
                />
            </a-form-item>
            <a-form-item :label="$t('phone')" name="phone">
                <a-input
                    v-model:value="formState.phone"
                    :placeholder="$t('placeholder_phone')"
                    :maxLength="11"
                >
                    <template #addonBefore>+86</template>
                </a-input>
            </a-form-item>
            <a-form-item :label="$t('Email')" name="email">
                <a-input
                    v-model:value="formState.email"
                    :placeholder="$t('placeholder_qingshuru')"
                    type="email"
                />
            </a-form-item>
            <a-form-item
                :label="$t('Initial Password')"
                name="initialPassword"
                v-if="!props.data"
            >
                <a-input
                    v-model:value="formState.initialPassword"
                    :placeholder="$t('placeholder_qingshuru')"
                    :type="passwordVisible ? 'text' : 'password'"
                    :maxLength="20"
                >
                    <template #suffix>
                        <div class="password-actions">
                            <a-button
                                type="text"
                                size="small"
                                @click="togglePasswordVisibility"
                                :title="
                                    passwordVisible
                                        ? $t('Hide Password')
                                        : $t('Show Password')
                                "
                            >
                                <template #icon>
                                    <EyeOutlined v-if="!passwordVisible" />
                                    <EyeInvisibleOutlined v-else />
                                </template>
                            </a-button>
                            <a-button
                                type="text"
                                size="small"
                                @click="copyPassword"
                                :title="$t('Copy')"
                            >
                                <template #icon>
                                    <CopyOutlined />
                                </template>
                            </a-button>
                        </div>
                    </template>
                </a-input>
            </a-form-item>
            <a-form-item
                :label="$t('user_quanxian')"
                name="permissions"
                v-if="isBooan"
            >
                <a-select
                    v-model:value="formState.permissions"
                    allowClear
                    mode="multiple"
                    :options="[
                        {
                            label: $t('user_EMSkongzhiquanxian'),
                            value: 'emsControl',
                        },
                    ]"
                    :placeholder="$t('placeholder_qingxuanzequanxian')"
                />
            </a-form-item>
            <a-form-item :label="$t('user_DataRange')" name="resourceScope">
                <a-select
                    v-model:value="formState.resourceScope"
                    :options="dataRanges"
                    :placeholder="$t('placeholder_qingxuanzeshujufanwei')"
                    @change="changeDataRange"
                />
            </a-form-item>
            <a-form-item
                :label="$t('kejianzhandian')"
                name="viewableStationIds"
            >
                <a-tree-select
                    v-model:value="formState.viewableStationIds"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="sites"
                    :placeholder="$t('qingxuanzekejianzhandian')"
                    tree-default-expand-all
                    multiple
                    :replaceFields="{
                        children: 'children',
                        title: 'name',
                        key: 'id',
                        value: 'id',
                    }"
                    :disabled="NodesDisabled"
                >
                </a-tree-select>
                <!-- <el-cascader :options="sites" :props="props2" clearable /> -->
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import {
    EyeOutlined,
    EyeInvisibleOutlined,
    CopyOutlined,
} from '@ant-design/icons-vue'
import { email, newPassword } from '@/common/reg'
import apiService from '@/apiService/device'

const { t, locale } = useI18n()
const labelCol = computed(() => {
    let res =
        locale.value == 'zh'
            ? {
                  span: 4,
              }
            : locale.value == 'en'
            ? {
                  span: 7,
              }
            : {
                  span: 7,
              }
    return res
})
const store = useStore()
const isBooan = computed(() => {
    return store.state.user.userInfoData.roles.includes('org_owner')
})

const props = defineProps({
    data: { type: Object },
    sites: { type: Array, default: () => [] },
})
const formState = reactive({
    staffId: void 0,
    realName: void 0,
    phone: void 0,
    email: void 0,
    initialPassword: void 0,
    permissions: [],
    resourceScope: void 0,
    viewableStationIds: [],
})

// 密码显示状态
const passwordVisible = ref(false)

const formRef = ref(null)

const validator = async (_rule, value) => {
    if (!value) {
        return Promise.reject(t('placeholder_phone'))
    } else {
        const phoneReg = /^1[3456789]\d{9}$/
        if (phoneReg.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(t('Please enter a valid phone number'))
        }
    }
}

// 邮箱验证器
const emailValidator = async (_rule, value) => {
    if (!value) {
        return Promise.reject(t('placeholder_qingshuru'))
    } else {
        if (email.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(t('Please enter a valid email'))
        }
    }
}

// 密码验证器
const passwordValidator = async (_rule, value) => {
    if (!value) {
        return Promise.reject(t('placeholder_qingshuru'))
    } else {
        if (newPassword.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(
                t(
                    'Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)'
                )
            )
        }
    }
}

const rules = {
    realName: [
        {
            required: true,
            message: t('placeholder_qingshuruxingming'),
            trigger: 'blur',
        },
    ],
    phone: [
        { required: true, trigger: ['change', 'blur'], validator: validator },
    ],
    email: [
        {
            required: true,
            trigger: ['change', 'blur'],
            validator: emailValidator,
        },
    ],
    initialPassword: [
        {
            required: true,
            trigger: ['change', 'blur'],
            validator: passwordValidator,
        },
    ],
    position: [{ required: false }],
    staffId: [{ required: false }],
}

const submitRules = async () => {
    if (!formState.resourceScope) {
        message.error(t('请选择数据范围'))
        return false
    }
    if (
        formState.resourceScope == 'specify' &&
        (!formState.viewableStationIds ||
            formState.viewableStationIds?.length <= 0)
    ) {
        message.error(t('请选择可见站点'))
        return false
    }

    // 表单验证
    try {
        await formRef.value.validate()
    } catch (error) {
        return false
    }

    // 重复检查逻辑
    if (props.data) {
        // 编辑用户：检查手机号和邮箱是否被其他用户占用
        const phoneExists = await checkPhoneDuplicate(
            formState.phone,
            props.data.staffId
        )
        const emailExists = await checkEmailDuplicate(
            formState.email,
            props.data.staffId
        )

        if (phoneExists || emailExists) {
            message.error(
                t(
                    'This phone number/email address is already in use, please change it'
                )
            )
            return false
        }
    } else {
        // 添加用户：检查邮箱是否重复
        const emailExists = await checkEmailDuplicate(formState.email)

        if (emailExists) {
            message.error(
                t('This email address is already in use, please change it')
            )
            return false
        }
    }

    return true
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
    passwordVisible.value = !passwordVisible.value
}

// 复制密码
const copyPassword = async () => {
    if (formState.initialPassword) {
        try {
            await navigator.clipboard.writeText(formState.initialPassword)
            message.success(t('Password copied to clipboard'))
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = formState.initialPassword
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            message.success(t('Password copied to clipboard'))
        }
    }
}

// 检查邮箱是否重复
const checkEmailDuplicate = async (email, excludeStaffId = null) => {
    try {
        // 这里需要调用检查邮箱重复的API
        // const response = await apiService.checkEmailExists({ email, excludeStaffId })
        // return response.data.exists

        // 临时返回false，实际需要根据API实现
        return false
    } catch (error) {
        console.error('检查邮箱重复失败:', error)
        return false
    }
}

// 检查手机号是否重复
const checkPhoneDuplicate = async (phone, excludeStaffId = null) => {
    try {
        // 这里需要调用检查手机号重复的API
        // const response = await apiService.checkPhoneExists({ phone, excludeStaffId })
        // return response.data.exists

        // 临时返回false，实际需要根据API实现
        return false
    } catch (error) {
        console.error('检查手机号重复失败:', error)
        return false
    }
}

const clearValidate = () => {
    formState.staffId = void 0
    formState.realName = void 0
    formState.phone = void 0
    formState.email = void 0
    formState.initialPassword = void 0
    formState.permissions = []
    passwordVisible.value = false
    formRef.value.clearValidate()
}

const resetFields = () => {
    formRef.value.resetFields()
}

defineExpose({ submitRules, clearValidate, resetFields, formState })

watch(
    () => props.data,
    (val) => {
        if (val) {
            Object.keys(val).forEach((key) => {
                formState[key] = val[key]
                if (key == 'permissions') {
                    formState[key] = val[key] ? val[key] : []
                }
            })
        }
    },
    { immediate: true }
)
const dataRanges = ref([
    {
        label: t('allData'),
        value: 'all',
    },
    {
        label: t('OnlySelectedData'),
        value: 'specify',
    },
])
const NodesDisabled = computed(() => {
    return formState.resourceScope == 'all'
})

const changeDataRange = (e) => {
    console.log(e)
    if (e == 'all') {
        formState.viewableStationIds = []
    } else {
        //
    }
}
</script>

<style lang="less" scoped>
.add-user {
    :deep(.ant-form) {
        .ant-form-item-has-success {
            margin-bottom: 24px !important;
        }
        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }
        .ant-form-item {
            font-size: 14px;
            margin-bottom: 24px;
            .ant-form-item-label {
                width: 90px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                color: rgba(34, 34, 34, 0.65);
                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
            }

            .ant-form-item-control-input {
                min-height: 32px;
                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 11px;
                        font-size: 14px;
                    }

                    .ant-input {
                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }

                    .ant-select-multiple {
                        font-size: 14px;
                        .ant-select-selector {
                            padding: 1px 4px;
                            font-size: 14px;
                            .ant-select-selection-item {
                                height: 24px;
                                margin-top: 2px;
                                margin-bottom: 2px;
                                line-height: 22px;
                                margin-inline-end: 4px;
                                padding-inline-start: 8px;
                                padding-inline-end: 8px;
                            }
                            &::after {
                                line-height: 24px;
                            }
                        }

                        .ant-select-selection-search-input,
                        .ant-select-multiple
                            .ant-select-selection-search-mirror {
                            height: 24px;
                        }
                    }

                    .ant-select {
                        &:not(.ant-select-disabled) {
                            &:hover {
                                .ant-select-selector {
                                    border-color: var(--themeColor);
                                }
                            }
                        }
                    }
                }
            }
        }

        .ant-select-focused {
            .ant-select-selector {
                border-color: var(--themeColor);
                box-shadow: none !important;
            }
        }
    }
}
.ant-select-dropdown {
    z-index: 9999 !important;
}
:deep(.ant-select-clear) {
    background-color: transparent;
    right: 30px;
}

.password-actions {
    display: flex;
    gap: 4px;

    .ant-btn {
        border: none;
        box-shadow: none;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .anticon {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }
    }
}
</style>
