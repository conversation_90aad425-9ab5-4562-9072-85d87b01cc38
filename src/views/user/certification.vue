<template>
    <div>
        <el-tabs v-model="authType" @tab-change="handleTabChange">
            <template #default>
                <el-tab-pane
                    :label="$t('SMS verification')"
                    name="sms"
                    :disabled="isSmsDisabled"
                >
                    <el-form
                        ref="stepOneFormRef"
                        style="max-width: 600px"
                        :model="stepOneForm"
                        :rules="stepOneFormRules"
                        label-width="auto"
                        class="stepOneForm"
                        hide-required-asterisk
                        :status-icon="false"
                    >
                        <el-form-item
                            :label="$t('OriginalPhone') + ':'"
                            :label-position="'left'"
                        >
                            <el-input :value="currentPhone" disabled>
                                <template #prefix>
                                    <div>+86</div>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Captcha') + ':'"
                            prop="captcha"
                            :label-position="'left'"
                        >
                            <el-input
                                v-model="stepOneForm.captcha"
                                :placeholder="$t('placeholder_qingshuru')"
                                :maxlength="4"
                            >
                                <template #append>
                                    <el-button
                                        plain
                                        type="primary"
                                        @click="sendCaptcha(1)"
                                        :disabled="isCaptchaDisabled"
                                    >
                                        {{ captchaButtonText }}
                                    </el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane
                    :label="$t('Email Verification')"
                    name="email"
                    :disabled="isEmailDisabled"
                >
                </el-tab-pane>
            </template>
        </el-tabs>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const props = defineProps({
    currentPhone: {
        type: String,
        default: '',
    },
    currentEmail: {
        type: String,
        default: '',
    },
})

const authType = ref('sms')
// 第一步表单数据
const stepOneFormRef = ref(null)
const stepOneForm = reactive({
    captcha: '',
})

const stepOneFormRules = {
    captcha: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        { min: 4, max: 4, message: '', trigger: 'blur' },
    ],
}
const handleTabChange = () => {
    //
}
// 验证手机号的正则表达式
const phoneReg = /^1[3-9]\d{9}$/
const captchaButtonText = ref(t('GetVerificationCode'))
// 发送验证码
let timer = null
let timer2 = null
const isCaptchaDisabled = ref(false)
const sendCaptcha = async (step) => {
    const phone = props.currentPhone
    if (step === 2 && !phoneReg.test(phone)) {
        ElMessage.error(t('Please enter a valid phone number'))
        return
    }
    // 这里添加发送验证码的接口调用
    if (step === 1) {
        // await service.sendSmsCode({ phone: phone, smsVerifyType: 'secondAuth' })
    } else {
        //
    }
    if (step === 1) {
        isCaptchaDisabled.value = true
        let count = 60
        timer = setInterval(() => {
            if (count > 0) {
                captchaButtonText.value = t('Captcha_tips02').replace(
                    's%',
                    count
                )
                count--
            } else {
                clearInterval(timer)
                captchaButtonText.value = t('GetVerificationCode')
                isCaptchaDisabled.value = false
            }
        }, 1000)
    }
}
// 根据props中的currentPhone 和currentEmail 判断tabs栏选项的禁用状态
const isSmsDisabled = computed(() => {
    return !props.currentPhone
})
const isEmailDisabled = computed(() => {
    return !props.currentEmail
})
// 监听currentPhone，如果有值，则默认选中短信验证
watch(
    () => props.currentPhone,
    (newVal) => {
        if (newVal) {
            authType.value = 'sms'
        } else {
            authType.value = 'email'
        }
    },
    {
        immediate: true,
    }
)
</script>

<style lang="less" scoped>
:deep(.el-tabs--bottom) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs--top) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}
:deep(.tags-tabs.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-bottom-color: transparent !important;
}
</style>
