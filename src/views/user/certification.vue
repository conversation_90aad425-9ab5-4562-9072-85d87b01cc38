<template>
    <div>
        <el-tabs v-model="authType" @tab-change="handleTabChange">
            <template #default>
                <el-tab-pane
                    :label="$t('SMS verification')"
                    name="sms"
                    :disabled="isSmsDisabled"
                >
                    <el-form
                        ref="stepOneFormRef"
                        style="max-width: 600px"
                        :model="stepOneForm"
                        :rules="stepOneFormRules"
                        label-width="auto"
                        class="stepOneForm"
                        hide-required-asterisk
                        :status-icon="false"
                    >
                        <el-form-item
                            :label="$t('OriginalPhone') + ':'"
                            :label-position="'left'"
                        >
                            <el-input :value="currentPhone" disabled>
                                <template #prefix>
                                    <div>+86</div>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Captcha') + ':'"
                            prop="captcha"
                            :label-position="'left'"
                        >
                            <el-input
                                v-model="stepOneForm.captcha"
                                :placeholder="$t('placeholder_qingshuru')"
                                :maxlength="4"
                            >
                                <template #append>
                                    <el-button
                                        plain
                                        type="primary"
                                        @click="sendCaptcha(1)"
                                        :disabled="isCaptchaDisabled"
                                    >
                                        {{ captchaButtonText }}
                                    </el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane
                    :label="$t('Email Verification')"
                    name="email"
                    :disabled="isEmailDisabled"
                >
                    <el-form
                        ref="emailFormRef"
                        style="max-width: 600px"
                        :model="emailForm"
                        :rules="emailFormRules"
                        label-width="auto"
                        class="emailForm"
                        hide-required-asterisk
                        :status-icon="false"
                    >
                        <el-form-item
                            :label="$t('Current Email') + ':'"
                            :label-position="'left'"
                        >
                            <el-input :value="currentEmail" disabled>
                            </el-input>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Captcha') + ':'"
                            prop="captcha"
                            :label-position="'left'"
                        >
                            <el-input
                                v-model="emailForm.captcha"
                                :placeholder="$t('placeholder_qingshuru')"
                                :maxlength="4"
                            >
                                <template #append>
                                    <el-button
                                        plain
                                        type="primary"
                                        @click="sendEmailCaptcha"
                                        :disabled="isEmailCaptchaDisabled"
                                    >
                                        {{ emailCaptchaButtonText }}
                                    </el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-form>
                </el-tab-pane>
            </template>
        </el-tabs>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import { email } from '@/common/reg'
import service from '@/apiService/device'
const { t } = useI18n()
const props = defineProps({
    currentPhone: {
        type: String,
        default: '',
    },
    currentEmail: {
        type: String,
        default: '',
    },
})

const authType = ref('sms')
// 第一步表单数据
const stepOneFormRef = ref(null)
const stepOneForm = reactive({
    captcha: '',
})

const stepOneFormRules = {
    captcha: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        { min: 4, max: 4, message: '', trigger: 'blur' },
    ],
}

// 邮箱验证表单数据
const emailFormRef = ref(null)
const emailForm = reactive({
    captcha: '',
})

const emailFormRules = {
    captcha: [
        {
            required: true,
            message: t('placeholder_qingshuru'),
            trigger: 'blur',
        },
        { min: 4, max: 4, message: '', trigger: 'blur' },
    ],
}
const handleTabChange = () => {
    //
}
// 验证手机号的正则表达式
const phoneReg = /^1[3-9]\d{9}$/
const captchaButtonText = ref(t('GetVerificationCode'))
// 邮箱验证码相关
const emailCaptchaButtonText = ref(t('GetVerificationCode'))
const isEmailCaptchaDisabled = ref(false)
// 发送验证码
let timer = null
let emailTimer = null
const isCaptchaDisabled = ref(false)
const sendCaptcha = async (step) => {
    const phone = props.currentPhone
    if (step === 2 && !phoneReg.test(phone)) {
        ElMessage.error(t('Please enter a valid phone number'))
        return
    }
    // 这里添加发送验证码的接口调用
    if (step === 1) {
        await service.sendSmsCode({ phone: phone, smsVerifyType: 'secondAuth' })
    } else {
        //
    }
    if (step === 1) {
        isCaptchaDisabled.value = true
        let count = 60
        timer = setInterval(() => {
            if (count > 0) {
                captchaButtonText.value = t('Captcha_tips02').replace(
                    's%',
                    count
                )
                count--
            } else {
                clearInterval(timer)
                captchaButtonText.value = t('GetVerificationCode')
                isCaptchaDisabled.value = false
            }
        }, 1000)
    }
}

// 发送邮箱验证码
const sendEmailCaptcha = async () => {
    const currentEmailValue = props.currentEmail
    if (!currentEmailValue) {
        ElMessage.error(t('Please enter a valid email'))
        return
    }

    // 验证邮箱格式
    if (!email.test(currentEmailValue)) {
        ElMessage.error(t('Please enter a valid email'))
        return
    }

    try {
        // 这里添加发送邮箱验证码的接口调用
        // await service.sendEmailCode({ email: currentEmailValue, emailVerifyType: 'secondAuth' })

        isEmailCaptchaDisabled.value = true
        let count = 60
        emailTimer = setInterval(() => {
            if (count > 0) {
                emailCaptchaButtonText.value = t('Captcha_tips02').replace(
                    's%',
                    count
                )
                count--
            } else {
                clearInterval(emailTimer)
                emailCaptchaButtonText.value = t('GetVerificationCode')
                isEmailCaptchaDisabled.value = false
            }
        }, 1000)

        ElMessage.success(t('Verification code sent successfully'))
    } catch (error) {
        ElMessage.error(t('Failed to send verification code'))
    }
}
// 根据props中的currentPhone 和currentEmail 判断tabs栏选项的禁用状态
const isSmsDisabled = computed(() => {
    return !props.currentPhone
})
const isEmailDisabled = computed(() => {
    return !props.currentEmail
})
// 监听currentPhone，如果有值，则默认选中短信验证
watch(
    () => props.currentPhone,
    (newVal) => {
        if (newVal) {
            authType.value = 'sms'
        } else {
            authType.value = 'email'
        }
    },
    {
        immediate: true,
    }
)

// 监听authType变化，清空验证码
watch(authType, () => {
    stepOneForm.captcha = ''
    emailForm.captcha = ''
})

// 计算当前验证码值
const currentCode = computed(() => {
    return authType.value === 'sms' ? stepOneForm.captcha : emailForm.captcha
})

// 计算当前邮箱
const currentEmail = computed(() => {
    return props.currentEmail
})

// 计算当前手机号
const currentPhone = computed(() => {
    return props.currentPhone
})

// 定义emit
const emit = defineEmits([
    'update:authType',
    'update:phone',
    'update:email',
    'update:code',
])

// 监听并emit数据变化
watch(
    authType,
    (newVal) => {
        emit('update:authType', newVal)
    },
    { immediate: true }
)

watch(
    () => props.currentPhone,
    (newVal) => {
        emit('update:phone', newVal)
    },
    { immediate: true }
)

watch(
    () => props.currentEmail,
    (newVal) => {
        emit('update:email', newVal)
    },
    { immediate: true }
)

watch(
    currentCode,
    (newVal) => {
        emit('update:code', newVal)
    },
    { immediate: true }
)
</script>

<style lang="less" scoped>
:deep(.el-tabs--bottom) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}

:deep(.el-tabs--top) {
    .el-tabs__item.is-bottom:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:last-child {
        padding: 0 6px;
    }

    .el-tabs__item.is-bottom:nth-child(2) {
        padding: 0 6px;
    }

    .el-tabs__item.is-top:nth-child(2) {
        padding: 0 6px;
    }
}
:deep(.tags-tabs.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
    border-bottom-color: transparent !important;
}
</style>
