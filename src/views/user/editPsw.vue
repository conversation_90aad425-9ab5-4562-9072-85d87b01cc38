<template>
    <el-drawer
        v-model="drawerVisible"
        :size="486"
        :lockScroll="true"
        :show-close="false"
        @close="closeDrawer"
    >
        <template #header>
            <div
                class="drawer-header flex items-center justify-between leading-5.5"
            >
                <div class="drawer-header">
                    <span>{{ $t('ChangePassword') }}</span>
                </div>
                <div class="flex gap-x-3" v-if="status === 1">
                    <el-button plain round @click="closeDrawer">{{
                        $t('Cancle')
                    }}</el-button>
                    <el-button plain round type="primary" @click="nextStep">{{
                        $t('NextStep')
                    }}</el-button>
                </div>
                <div class="flex gap-x-3" v-if="status === 2">
                    <el-button plain round @click="prevStep">{{
                        $t('PreviousStep')
                    }}</el-button>
                    <el-button
                        plain
                        round
                        type="primary"
                        @click="confirmChange"
                        :disabled="!btnDisabled"
                        >{{ $t('ConfirmTheChanges') }}</el-button
                    >
                </div>
            </div>
        </template>
        <div v-if="status === 1">
            <el-form
                ref="stepOneFormRef"
                style="max-width: 600px"
                :model="stepOneForm"
                :rules="stepOneFormRules"
                label-width="auto"
                class="stepOneForm"
                hide-required-asterisk
                :status-icon="false"
            >
                <el-form-item
                    :label="$t('phone') + ':'"
                    :label-position="'left'"
                >
                    <el-input :value="currentPhone" disabled />
                </el-form-item>
                <el-form-item
                    :label="$t('Captcha') + ':'"
                    prop="captcha"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepOneForm.captcha"
                        :placeholder="$t('placeholder_qingshuru')"
                        :maxlength="4"
                    >
                        <template #append>
                            <el-button
                                plain
                                type="primary"
                                @click="sendCaptcha"
                                :disabled="isCaptchaDisabled"
                            >
                                {{ captchaButtonText }}
                            </el-button>
                        </template>
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
        <div v-if="status === 2">
            <el-form
                ref="stepTwoFormRef"
                style="max-width: 600px"
                :model="stepTwoForm"
                :rules="stepTwoFormRules"
                label-width="auto"
                class="stepTwoForm"
                hide-required-asterisk
                :status-icon="false"
            >
                <el-form-item
                    :label="$t('NewPassword')"
                    prop="newPassword"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.newPassword"
                        type="password"
                        :placeholder="$t('Password_tips01')"
                        show-password
                        :maxlength="32"
                    />
                </el-form-item>
                <el-form-item
                    :label="$t('ConfirmPassword')"
                    prop="confirmPassword"
                    :label-position="'left'"
                >
                    <el-input
                        v-model="stepTwoForm.confirmPassword"
                        type="password"
                        :placeholder="$t('Password_tips02')"
                        show-password
                        :maxlength="32"
                    />
                </el-form-item>
            </el-form>
        </div>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import service from '@/apiService/device'
import { useStore } from 'vuex'
import api from '@/apiService/index'
import Cookies from 'js-cookie'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const store = useStore()
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    currentPhone: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['update:visible', 'confirm'])

const drawerVisible = computed({
    get() {
        return props.visible
    },
    set(value) {
        emit('update:visible', value)
    },
})

// 当前步骤状态
const status = ref(1)

// 表单ref
const stepOneFormRef = ref(null)
const stepTwoFormRef = ref(null)

// 第一步表单数据
const stepOneForm = reactive({
    captcha: '',
})

// 第二步表单数据
const stepTwoForm = reactive({
    newPassword: '',
    confirmPassword: '',
})
const regex = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>/?]{8,32}$/
const btnDisabled = computed(() => {
    return (
        regex.test(stepTwoForm.newPassword) &&
        stepTwoForm.newPassword === stepTwoForm.confirmPassword
    )
})
// 验证新密码是否符合规则
const validatePassword = (rule, value, callback) => {
    if (!value) {
        callback(new Error(t('Password_tips03')))
    } else if (value.length < 8) {
        callback(new Error(t('Password_tips04')))
    } else if (!regex.test(value)) {
        callback(new Error(t('Password_tips05')))
    } else {
        callback()
    }
}

// 验证两次密码是否一致
const validateConfirmPassword = (rule, value, callback) => {
    if (!value) {
        callback(new Error(t('Password_tips06')))
    } else if (value !== stepTwoForm.newPassword) {
        callback(new Error(t('Password_tips07')))
    } else {
        callback()
    }
}

// 表单验证规则
const stepOneFormRules = {
    captcha: [
        { required: true, message: t('PleaseInputCaptcha'), trigger: 'blur' },
        { min: 4, max: 4, message: t('Captcha_tips01'), trigger: 'blur' },
    ],
}

const stepTwoFormRules = {
    newPassword: [
        { required: true, validator: validatePassword, trigger: 'blur' },
    ],
    confirmPassword: [
        { required: true, validator: validateConfirmPassword, trigger: 'blur' },
    ],
}

// 验证码按钮相关
const isCaptchaDisabled = ref(false)
const captchaButtonText = ref(t('GetVerificationCode'))
let timer = null

// 发送验证码
const sendCaptcha = async () => {
    await service.sendSmsCode({
        phone: props.currentPhone,
        smsVerifyType: 'secondAuth',
    })
    isCaptchaDisabled.value = true
    let count = 60
    timer = setInterval(() => {
        if (count > 0) {
            captchaButtonText.value = t('Captcha_tips02').replace('s%', count)
            count--
        } else {
            clearInterval(timer)
            captchaButtonText.value = t('GetVerificationCode')
            isCaptchaDisabled.value = false
        }
    }, 1000)
}

// 下一步
const nextStep = async () => {
    if (!stepOneFormRef.value) return
    await stepOneFormRef.value.validate(async (valid) => {
        if (valid) {
            let res = await service.secondAuth({
                phone: props.currentPhone,
                smsCode: stepOneForm.captcha,
            })
            if (res.data.data) {
                status.value = 2
                stepOneForm.captcha = ''
            }
        }
    })
}

// 上一步
const prevStep = () => {
    status.value = 1
    stepTwoForm.newPassword = ''
    stepTwoForm.confirmPassword = ''
}

// 确认修改
const confirmChange = async () => {
    if (!stepTwoFormRef.value) return
    await stepTwoFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                let res = await api.updateMeInfo({
                    password: stepTwoForm.newPassword,
                })
                if (res.data.data) {
                    ElMessage.success(t('Password_tips08'))
                    store.commit('user/setHidetip', true)
                    Cookies.set('modifyPasswordFlag', 1, {
                        expires: 7,
                        path: '/',
                        domain: 'ssnj.com',
                    })
                    emit('confirm', 'updatePsw')
                    closeDrawer()
                }
            } catch (error) {
                ElMessage.error(t('Password_tips09'))
            }
        }
    })
}

// 关闭抽屉
const closeDrawer = () => {
    if (status.value === 1) stepOneFormRef.value.resetFields()
    if (status.value === 2) stepTwoFormRef.value.resetFields()
    status.value = 1
    stepOneForm.captcha = ''
    stepTwoForm.newPassword = ''
    stepTwoForm.confirmPassword = ''
    if (timer) clearInterval(timer)
    captchaButtonText.value = t('GetVerificationCode')
    isCaptchaDisabled.value = false

    emit('update:visible', false)
}
</script>

<style scoped></style>
