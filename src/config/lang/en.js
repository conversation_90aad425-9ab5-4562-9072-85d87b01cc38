export default {
  "":"",
  "null": "No more data",
  "A001": "hello world",
  "Axiangdianliu": "A-phase I",
  "Axiangdianya": "A-phase Voltage",
  "Axiangwugonggonglv": "A-phase Q",
  "Axiangyougonggonglv": "A-phase P",
  "Axiangzongshizaigonglv": "A-Total Apparent Power",
  "Bxiangdianliu": "B-phase I",
  "Bxiangdianya": "B-phase Voltage",
  "Bxiangwugonggonglv": "B-phase Q",
  "Bxiangyougonggonglv": "B-phase P",
  "Bxiangzongshizaigonglv": "B-Total Apparent Power",
  "Captcha": "Captcha",
  "Captcha_tips01": "The length should be 4 characters",
  "Captcha_tips02": "Retry after s% seconds",
  "ChangePassword": "Change Password",
  "ChangePhone": "Modify Phone Number",
  "ConfirmPassword": "Confirm Password",
  "ConfirmTheChanges": "Confirm",
  "Cxiangdianliu": "C-phase I",
  "Cxiangdianya": "C-phase Voltage",
  "Cxiangwugonggonglv": "C-phase Q",
  "Cxiangyougonggonglv": "C-phase P",
  "Cxiangzongshizaigonglv": "C-Total Apparent Power",
  "DingTalk": "DingTalk",
  "DingTalkMiniProgram": "DingTalk Mini Program",
  "Export_daochu": "Export",
  "Flat": "Flat",
  "GetVerificationCode": "Get verification code",
  "NewPassword": "New Password",
  "NewPhone": "New Phone",
  "NextStep": "Next step",
  "OnPeak": "Peak",
  "OriginalPhone": "Original Phone",
  "Password_tips01": "Please enter new password",
  "Password_tips02": "Please re-enter new password",
  "Password_tips03": "Please enter password",
  "Password_tips04": "At least 8 characters required",
  "Password_tips05": "Support input of numbers, English letters or English symbols",
  "Password_tips06": "Please re-enter your password",
  "Password_tips07": "The passwords you entered twice do not match",
  "Password_tips08": "Modification successful, please log in again",
  "Password_tips09": "Operation failed, try again later",
  "Password_tips10": "Password length must not be less than 6 characters",
  "Peak": "Top",
  "PleaseInputCaptcha": "Please input verification code",
  "PreviousStep": "Previous step",
  "Valley": "Valley",
  "WeChat": "WeChat",
  "alarm_chulirenyuan": "Handler",
  "alarm_chulishichang": "Processing Time",
  "alarm_gaojingmingcheng": "Alarm Name",
  "alarm_jiguibianhao": "Cabinet No",
  "alarm_options_0_4": "0-4 Hours",
  "alarm_options_1_3": "1-3 Days",
  "alarm_options_3more": "3 Days+",
  "alarm_options_4_8": "4-8 Hours",
  "alarm_options_8_24": "8-24 Hours",
  "alarm_options_chulizhong": "Processing",
  "alarm_options_gaopinguzhang": "Frequent Faults",
  "alarm_options_gaoyouguzhang": "High-Priority Faults",
  "alarm_options_quanbushichang": "All Durations",
  "alarm_options_quanbuzhuangtai": "All",
  "alarm_options_shoudonggongdan": "Manual Work Order",
  "alarm_options_shoudongguzhang": "Manual Faults",
  "Resolved": "Resolved",
  "alarm_quanbuleixing": "All Types",
  "alarm_quanburenyuan": "All Personnel",
  "alarm_yichangleixing": "Anomaly Type",
  "alarm_yichangliebiao": "Anomaly List",
  "alarm_yichangmingcheng": "Anomaly Name",
  "alarm_yichangtongji": "Anomaly Statistics",
  "alarm_yichangzhuangtai": "Anomaly Status",
  "alarm_zanwuyichang": "No Abnormalities",
  "alarm_zhandianmingcheng": "Station Name",
  "biangengriqi": "Changing the commissioning date, please proceed with caution!",
  "bianjizhandianxinxi": "Edit Station Info",
  "bingwang": "On-grid",
  "buchongshuoming": "Additional Explanation",
  "buchongxinxi": "Additional Info",
  "caozuochenggong": "Success",
  "Vehicles": "Vehicles",
  "car_cheliangzongshu": "Total Vehicles",
  "car_chezhanzongshu": "Total Stations",
  "car_dianxingeshu": "Number of Cells",
  "Cell type": "Cell type",
  "car_edingrongliang": "Rated Capacity",
  "car_leijixunhuancishu": "Cycle Count",
  "car_lixiancheliang": "Offline",
  "car_qianyiriwushuju": "No Data for the Previous Day",
  "car_tips01": "No real-time cell data available",
  "car_yueleijihuoyueshu": "Monthly Active Count",
  "car_zaixiancheliang": "Online",
  "car_zongjihuoyueshu": "Total Active Count",
  "car_zuorihuoyuecheliang": "Yesterday's Active Vehicles",
  "changePwd01": "Password Change Recommended",
  "changePwd02": "we recommend changing your initial password immediately after your first login.",
  "changeUsername": "Modify Username",
  "chezhandizhi": "Station Address",
  "chezhanleixing": "Station Type",
  "chezhanmingcheng": "Station Name",
  "chezhanzuobiao": "Station Coordinates",
  "chongdianchengben": "Charging Cost",
  "Charge/Discharge Ranking": "Charge/Discharge Ranking",
  "chuangdu_tips": "The length is between s% and e% characters",
  "chuangjianshijian": "Create Time",
  "All Abnormal":"全部异常",
  "ciyao": "minor",
  "code_10011": "Organization Not Activated",
  "code_10012": "Phone number authorization required",
  "code_10013": "The phone number is not registered. Please verify and re-enter.",
  "code_10040": "SMS verification code validation failed",
  "code_10041": "SMS sending failed",
  'code_error': 'Abnormal, please try again later.',
  "Save": "Save",
  "common_bianji": "Edit",
  "common_fou": "No",
  "common_ge": '',
  "common_guanbi": "Close",
  "common_hour": "Hour",
  "common_jieshiriqi": "End Date",
  "common_jieshuriqi": "End Date",
  "common_jieshushijian": "End Time",
  "common_jin30tian": "Last 30 Days",
  "common_jintian": "Today",
  "common_jinyinian": "Last Year",
  "common_jinyizhou": "Last Week",
  "common_kaishiriqi": "Start Date",
  "common_kaishishijian": "Start Time",
  "common_liang": '',
  "common_nian": "Year",
  "Cancle": "Cancle",
  "common_ri": "Day",
  "common_rihuizong": "Daily Summary",
  "common_riqi": "Date",
  "common_shi": "Yes",
  "common_tian": "Day",
  "common_tiao": "",
  "common_tiaozhi": "Jump to",
  "common_wanyuan": '',
  "common_xiaoshihuizong": "Hourly Summary",
  "common_ye": "Page",
  "common_yuan": '',
  "common_yue": "Month",
  "common_yuehuizong": "Monthly Summary",
  "common_zuo": '',
  "daichuli": "Pending",
  "device_chanpinxinghao": "Product Model",
  "device_chushi": "dehumidify",
  "device_dianxinfenxi": "Cell Analysis",
  "device_dianxinxinghao": "Cell Model",
  "device_fangchafenxi": "Cell Variance Analysis",
  "device_jichafenxi": "Cell Range Analysis",
  "device_jueyuanzhi": "Insulation Resistance",
  "device_kechongdianliang": "Rechargeable Capacity",
  "device_kefangdianliang": "Dischargeable Capacity",
  "device_leijichongdianliang": "Total Charging",
  "device_leijifangdianliang": "Total Discharging",
  "device_pingjundianya": "Avg Voltage",
  "device_pingjunwendu": "Avg Temp",
  "Device No": "Device No",
  "device_shebeimingcheng": "Device Name",
  "device_shebeixinghao": "Device Name",
  "device_shebeixuhao": "Device Serial Number",
  "device_suoshubianyaqi": "Associated Transformer",
  "device_type_chunengdianbiao": "Energy Meter",
  "device_type_chushiji": "Dehumidifier",
  "device_type_cu": "Rack",
  "device_type_dianwangdianbiao": "Grid Meter",
  "Cell": "Cell",
  "device_type_dui": "Cluster",
  "device_type_xiaofang": "Fire Protection",
  "device_type_yelengji": "Liquid Cooler",
  "device_zongdianliu": "Total Current",
  "device_zongdianya": "Total Voltage",
  "device_zuididianya": "Min Voltage",
  "device_zuidiwendu": "Min Temp",
  "device_zuigaodianya": "Max Voltage",
  "device_zuigaowendu": "Max Temp",
  "dianchibao": "Battery Pack s%",
  "dianliubianbi": "Current Ratio",
  "dianyabianbi": "Voltage Ratio",
  "ditu": "Map",
  "energy_storage_cabinet.realData.CU.avgTemperature": "Avg Temp",
  "energy_storage_cabinet.realData.CU.avgVoltage": "Avg Voltage",
  "energy_storage_cabinet.realData.CU.chargeStatus": "Charge/Discharge Status",
  "energy_storage_cabinet.realData.CU.electricity": "Total Current",
  "energy_storage_cabinet.realData.CU.irn": "Negative Insulation Resistance",
  "energy_storage_cabinet.realData.CU.irp": "Positive Insulation Resistance",
  "energy_storage_cabinet.realData.CU.maxChargeElectricity": "Max Charging Current",
  "energy_storage_cabinet.realData.CU.maxChargePower": "Max Charging Power",
  "energy_storage_cabinet.realData.CU.maxDischargeElectricity": "Max Discharging Current",
  "energy_storage_cabinet.realData.CU.maxDischargePower": "Max Discharging Power",
  "energy_storage_cabinet.realData.CU.maxSoc": "Max Cell SOC",
  "energy_storage_cabinet.realData.CU.maxSocId": "Max Cell SOC Index",
  "energy_storage_cabinet.realData.CU.maxSoh": "Max Cell SOH",
  "energy_storage_cabinet.realData.CU.maxSohId": "Max Cell SOH Index",
  "energy_storage_cabinet.realData.CU.maxTemperature": "Max Cell Temp",
  "energy_storage_cabinet.realData.CU.maxTemperatureId": "Max Cell Temp Index",
  "energy_storage_cabinet.realData.CU.maxVoltage": "Max Cell Voltage",
  "energy_storage_cabinet.realData.CU.maxVoltageId": "Max Cell Voltage Index",
  "energy_storage_cabinet.realData.CU.minSoc": "Min Cell SOC",
  "energy_storage_cabinet.realData.CU.minSocId": "Min Cell SOC Index",
  "energy_storage_cabinet.realData.CU.minSoh": "Max Cell SOH",
  "energy_storage_cabinet.realData.CU.minSohId": "Max Cell SOH Index",
  "energy_storage_cabinet.realData.CU.minTemperature": "Min Cell Temp",
  "energy_storage_cabinet.realData.CU.minTemperatureId": "Min Cell Temp Index",
  "energy_storage_cabinet.realData.CU.minVoltage": "Min Cell Voltage",
  "energy_storage_cabinet.realData.CU.minVoltageId": "Min Cell Voltage Index",
  "energy_storage_cabinet.realData.CU.runStatus": "Operating",
  "energy_storage_cabinet.realData.CU.singleChargeQuantity": "Single Charge Energy",
  "energy_storage_cabinet.realData.CU.singleDischargeQuantity": "Single Discharge Energy",
  "energy_storage_cabinet.realData.CU.soc": "SOC",
  "energy_storage_cabinet.realData.CU.soh": "SOH",
  "energy_storage_cabinet.realData.CU.totalChargeQuantity": "Total Charge Energy",
  "energy_storage_cabinet.realData.CU.totalDischargeQuantity": "Total Discharge Energy",
  "energy_storage_cabinet.realData.CU.voltage": "Total Voltage",
  "energy_storage_cabinet.realData.DB.ct": "Current Ratio",
  "energy_storage_cabinet.realData.DB.epn": "Reverse Active Energy",
  "energy_storage_cabinet.realData.DB.epp": "Forward Active Energy",
  "energy_storage_cabinet.realData.DB.freq": "Freq",
  "energy_storage_cabinet.realData.DB.ia": "A-phase I",
  "energy_storage_cabinet.realData.DB.ib": "B-phase I",
  "energy_storage_cabinet.realData.DB.ic": "C-phase I",
  "energy_storage_cabinet.realData.DB.p": "Total Active Power",
  "energy_storage_cabinet.realData.DB.pa": "A-phase P",
  "energy_storage_cabinet.realData.DB.pb": "B-phase P",
  "energy_storage_cabinet.realData.DB.pc": "C-phase P",
  "energy_storage_cabinet.realData.DB.pf": "Total Power Factor",
  "energy_storage_cabinet.realData.DB.pfa": "A-phase Power Factor",
  "energy_storage_cabinet.realData.DB.pfb": "B-phase Power Factor",
  "energy_storage_cabinet.realData.DB.pfc": "C-phase Power Factor",
  "energy_storage_cabinet.realData.DB.pt": "Voltage Ratio",
  "energy_storage_cabinet.realData.DB.q": "Total Apparent Power",
  "energy_storage_cabinet.realData.DB.qa": "A-phase S",
  "energy_storage_cabinet.realData.DB.qb": "B-phase S",
  "energy_storage_cabinet.realData.DB.qc": "C-phase S",
  "energy_storage_cabinet.realData.DB.s": "Total Reactive Power",
  "energy_storage_cabinet.realData.DB.sa": "A-phase Q",
  "energy_storage_cabinet.realData.DB.sb": "B-phase Q",
  "energy_storage_cabinet.realData.DB.sc": "C-phase Q",
  "energy_storage_cabinet.realData.DB.ua": "A-phase Voltage",
  "energy_storage_cabinet.realData.DB.uab": "AB Voltage",
  "energy_storage_cabinet.realData.DB.ub": "B-phase Voltage",
  "energy_storage_cabinet.realData.DB.ubc": "BC Voltage",
  "energy_storage_cabinet.realData.DB.uc": "C-phase Voltage",
  "energy_storage_cabinet.realData.DB.uca": "CA Voltage",
  "energy_storage_cabinet.realData.DI.doorOpen": "Cabinet Door Switch",
  "energy_storage_cabinet.realData.DI.emergencyStop": "Emergency Stop Switch",
  "energy_storage_cabinet.realData.DI.waterSoak": "Water Immersion",
  "energy_storage_cabinet.realData.DUI.chargeCapacity": "Rechargeable Capacity",
  "energy_storage_cabinet.realData.DUI.chargeStatus": "Charge/Discharge Status",
  "energy_storage_cabinet.realData.DUI.dischargeCapacity": "Dischargeable Capacity",
  "energy_storage_cabinet.realData.DUI.highestBatteryTemp": "Max Cell Temp",
  "energy_storage_cabinet.realData.DUI.highestBatteryVoltage": "Max Cell Voltage",
  "energy_storage_cabinet.realData.DUI.insulationValue": "System Insulation Value",
  "energy_storage_cabinet.realData.DUI.lowestBatteryTemp": "Min Cell Temp",
  "energy_storage_cabinet.realData.DUI.lowestBatteryVoltage": "Min Cell Voltage",
  "energy_storage_cabinet.realData.DUI.runStatus": "Operating",
  "energy_storage_cabinet.realData.DUI.soc": "SOC",
  "energy_storage_cabinet.realData.DUI.soh": "SOH",
  "energy_storage_cabinet.realData.DUI.totalElectricity": "Total Current",
  "energy_storage_cabinet.realData.DUI.totalVoltage": "Total Voltage",
  "energy_storage_cabinet.realData.FIRE.coConc": "CO Concentration",
  "energy_storage_cabinet.realData.FIRE.hydrogenConc": "Hydrogen Concentration",
  "energy_storage_cabinet.realData.FIRE.smokeConc1": "Smoke Concentration 1",
  "energy_storage_cabinet.realData.FIRE.smokeConc2": "Smoke Concentration 2",
  "energy_storage_cabinet.realData.FIRE.temp": "Temp",
  "energy_storage_cabinet.realData.KT.environmentHumidity": "Ambient Humidity",
  "energy_storage_cabinet.realData.KT.environmentTemp": "Ambient Temp",
  "energy_storage_cabinet.realData.SXBLQ.containerTemp": "Module Temp",
  "energy_storage_cabinet.realData.SXBLQ.dcChargeStatus": "DC Charging",
  "energy_storage_cabinet.realData.SXBLQ.dcDisChargeStatus": "DC Discharging",
  "energy_storage_cabinet.realData.SXBLQ.dcv1": "DC High Voltage",
  "energy_storage_cabinet.realData.SXBLQ.dcv2": "DC Low Voltage",
  "energy_storage_cabinet.realData.SXBLQ.errorStatus": "Overall Fault Status",
  "energy_storage_cabinet.realData.SXBLQ.ia": "A-phase I",
  "energy_storage_cabinet.realData.SXBLQ.ib": "B-phase I",
  "energy_storage_cabinet.realData.SXBLQ.ic": "C-phase I",
  "energy_storage_cabinet.realData.SXBLQ.igbtTemp": "IGBT Temp",
  "energy_storage_cabinet.realData.SXBLQ.onGridStatus": "Grid Connection",
  "energy_storage_cabinet.realData.SXBLQ.p": "3-Phase Active Power",
  "energy_storage_cabinet.realData.SXBLQ.pa": "A-phase P",
  "energy_storage_cabinet.realData.SXBLQ.pb": "B-phase P",
  "energy_storage_cabinet.realData.SXBLQ.pc": "C-phase P",
  "energy_storage_cabinet.realData.SXBLQ.q": "3-Phase Apparent Power",
  "energy_storage_cabinet.realData.SXBLQ.qa": "A-phase S",
  "energy_storage_cabinet.realData.SXBLQ.qb": "B-phase S",
  "energy_storage_cabinet.realData.SXBLQ.qc": "C-phase S",
  "energy_storage_cabinet.realData.SXBLQ.runStatus": "Runing Status",
  "energy_storage_cabinet.realData.SXBLQ.s": "3-Phase Reactive Power",
  "energy_storage_cabinet.realData.SXBLQ.sa": "A-phase Q",
  "energy_storage_cabinet.realData.SXBLQ.sb": "B-phase Q",
  "energy_storage_cabinet.realData.SXBLQ.sc": "C-phase Q",
  "energy_storage_cabinet.realData.SXBLQ.stt": "Start/Stop Status",
  "energy_storage_cabinet.realData.SXBLQ.ua": "Port AB Line Voltage",
  "energy_storage_cabinet.realData.SXBLQ.ub": "Port BC Line Voltage",
  "energy_storage_cabinet.realData.SXBLQ.uc": "Port CA Line Voltage",
  "energy_storage_cabinet.realData.YLJ.environmentTemp": "Ambient Temp",
  "energy_storage_cabinet.realData.YLJ.waterInletPressure": "Inlet Pressure",
  "energy_storage_cabinet.realData.YLJ.waterInletTemp": "Inlet Temp",
  "energy_storage_cabinet.realData.YLJ.waterOutletPressure": "Outlet Pressure",
  "energy_storage_cabinet.realData.YLJ.waterOutletTemp": "Outlet Temp",
  "energy_storage_cabinet.realData.YLJ.workStatus": "Operating Mode",
  "enterprise_dangqiansezhi": "Current Color Value",
  "enterprise_dengluyebeijing": "Login Page Background",
  "enterprise_qiyeguanli": "Enterprise",
  "enterprise_qiyelogo": "Enterprise Logo",
  "enterprise_qiyemingcheng": "Enterprise Name",
  "enterprise_qiyexinxi": "Enterprise Info",
  "enterprise_qiyeyanse": "Enterprise Color",
  "enterprise_qiyezhuti": "Enterprise Theme",
  "enterprise_tips001": "The administrator is the only user with the highest privileges on the application side, having all operational and data permissions.",
  "enterprise_tips002": "The current color value can be switched for system theme color configuration.",
  "enterprise_tips003": "If you need to customize the system's primary domain, please refer to this document for instructions.",
  "enterprise_tips004": "Enterprise Name Cannot Be Empty",
  "enterprise_wangyebiaoti": "Web Page Title",
  "enterprise_wangyeico": "Web Page Icon",
  "enterprise_wangyeicon": "Web Page Icon",
  "enterprise_wangyeyuming": "Web Page Domain",
  "enterprise_zhuguanliyuan": "Administrator",
  "export_tips01": "Would you like to export data from s% to s%?",
  "fangdianshouyi": "Discharging Revenue",
  "fanxiangyougongzongdianneng": "Reverse Active Energy",
  "fashengyuanyin": "Cause",
  "gaojing": "Alarm",
  "gaojingbianhao": "Alarm No",
  "gaojingjibie": "Alarm Level",
  "gaojingzhuangtai": "Alarm Status",
  "genjinren": "Follow-up Person",
  "gongdan": "Work Order",
  "gongdanbianhao": "Work Order No",
  "gongdanleixing": "Work Order Type",
  "gongdanliebiao": "Work Order List",
  "gongdanmingcheng": "Work Order Name",
  "gongdantongji": "Work Order Statistics",
  "gongdanxiangqing": "Work Order Detail",
  "gongdanzhuangtai": "Work Order Status",
  "gonglvyinshu": "Power Factor",
  "guanliangaojingxinxi": "Related Alarm Info",
  "guanliyuan": "Administrator",
  "haobianyaqi": "Transformer n%",
  "haobianyaqirongliang": "Transformer n% Capacity",
  "home_dianliangzhibiao": "Energy Metrics",
  "home_fugaichengshi": "Covered Cities",
  "home_shouyizhibiao": "Revenue Metrics",
  "home_zhandianliebiao": "Station List",
  "home_zhandianzongshu": "Station Count",
  "huluegaojing": "Ignore Alarm",
  "jiazaigengduo": "Load More",
  "jibie": "Level",
  "jiejue": "Resolve",
  "jieshuyuefen": "End Mth",
  "jiesuo": "Unlock",
  "jinji": "urgent",
  "kaishiyuefen": "Start Mth",
  "latitude": "latitude",
  "leixing": "Type",
  "liebiao": "List",
  "lijichakan": "View",
  "lijiqianwang": "Go Now",
  "liwang": "Off-grid",
  "login_anquantuichu": "Log Out",
  "login_daojishi": "Countdown",
  "login_huoquyanzhengma": "Send Code",
  "login_login": "Login",
  "login_mimadenglu": "Password Login",
  "login_tips01": "I understand and agree to the User Agreement to assist or login",
  "login_tips02": "Please enter the SMS verification code",
  "login_weidenglu": "Not Logged In",
  "login_yanshizhanghaodenglu": "Demo Account Login",
  "Verification code login": "Phone Login",
  "longitude": "longitude",
  "menu_anquantuichu": "Log Out",
  "Alert Notification": "Alert Notification",
  "Settings": "Settings",
  "User Guide": "User Guide",
  "Add Station": "Add Station",
  "AddStation": "Add Station",
  "Account": "Account",
  "Management": "Management",
  "Dashboard": "Dashboard",
  "message_429": "Frequent Requests, Please Try Again Later",
  "message_wrongSMSCode": "SMS verification code validation failed",
  "Reset Success!": "Reset Success!",
  "No Station Bound Yet": "No Station Bound Yet",
  "URL address not configured yet": "URL address not configured yet",
  "The maximum date range cannot exceed 7 days": "The maximum date range cannot exceed 7 days",
  "Name": "Name",
  "tips_bind_r": "to view device operation status and data online.",
  "tips_bind_l": "After binding your account, you can access the",
  "DingTalkScan": "Bind via DingTalk QR Code",
  "DingTalkBind": "DingTalk Account Binding",
  "Basic Info": "Basic Info",
  "Password": "Password",
  "WeChatScan": "Bind via WeChat QR Code",
  "WeChatBind": "WeChat Account Binding",
  "tips_bind_DingTalk": "DingTalk account bound: s%, scan the QR code to access.",
  "tips_bind_WeChat": "WeChat account bound: s%, scan the QR code to access.",
  "Account Binding": "Account Binding",
  "No more data": "No more data",
  "opeartion_caozuoneirong": '',
  "opeartion_caozuoren": '',
  "opeartion_caozuorizhi": '',
  "opeartion_fashengshijian": '',
  "opeartion_options_dianjiaguanli": '',
  "opeartion_options_donghuanguanli": '',
  "opeartion_options_nengxiaoguanli": '',
  "opeartion_options_qiyeguanli": '',
  "opeartion_options_shujudaochu": '',
  "opeartion_options_xunidianchang": '',
  "opeartion_options_yonghuguanli": '',
  "opeartion_shijianleixing": '',
  "Bind": "Bind",
  "Edit User": "Edit User",
  "Edit Sub-account": "Edit Sub-account",
  "View Cell Analysis": "View Cell Analysis",
  "Return to Overall Device": "Return to Overall Device",
  "Copy": "Copy",
  "Unbind": "Unbind",
  "Delete": "Delete",
  "tips_unbind_DingTalk": "Are you sure you want to unbind this account?",
  "tips_unbind_WeChat": "Are you sure you want to unbind this account?",
  "Refresh": "Refresh",
  "add_device": "Add Device",
  "Add User": "Add User",
  "Add Sub-account": "Add Sub-account",
  "Edit": "Edit",
  "Rank": "Rank",
  "phone": "Phone",
  "tell": "Phone",
  "Mail":"Mail",
  "Phone/Mail":"Phone / Mail",
  "Please enter a valid phone number": "Please enter a valid phone number",
  "phone_tips02": "New number cannot be the same as old number",
  "placeholder": "Input content search",
  "placeholder_phone": "Please enter the phone number",
  "placeholder_qingshuru": "Please enter",
  "placeholder_qingshuruguanliyuanshoujihaoma": "Please enter the administrator's phone number",
  "placeholder_qingshuruguanliyuanxingmiing": "Please enter the administrator's name",
  "placeholder_qingshuruguanliyuanxingming": "Please enter the administrator's name",
  "placeholder_qingshurumima": "Please enter your password",
  "placeholder_qingshuruxingmiing": "Please enter the name",
  "placeholder_qingshuruxingming": "Please enter the name",
  "placeholder_qingshuruzhandianbianhao": "Please enter the station number",
  "placeholder_qingshuruzhandianmingcheng": "Please enter the station name",
  "placeholder_qingshuruzizhanghumingcheng": "Please enter the sub-account name",
  "placeholder_qingxuanze": "Please Select",
  "placeholder_qingxuanzebangdingqiye": "Please select an enterprise to bind",
  "placeholder_qingxuanzebianyaqishuliang": "Please select the number of transformers",
  "placeholder_qingxuanzequanxian": "Please select permissions",
  "placeholder_qingxuanzeriqi": "Please select the date",
  "placeholder_qingxuanzesuoshubianyaqi": "Please select the associated transformer",
  "placeholder_qingxuanzeweihurenyuan": "Please select maintenance personnel",
  "placeholder_qingxuanzeyewufanwei": "Please select the business scope",
  "placeholder_qingxuanzezhandiandiqu": "Please select the region",
  "placeholder_qingxuanzezhandiandizhi": "Please select the address",
  "placeholder_sousuodidian": "Search Location",
  "qingshangchuantupian": "Place upload picture",
  "qingxuanzechezhanleixing": "Please select station type",
  "qingxuanzegenjinren": "Please Select Follow-up Person",
  "qita": "other",
  "All": "All",
  "All Level": "All Level",
  "Confirm": "Confirm",
  "Date Select": "Date Select",
  "Upload Failed": "Upload Failed",
  "Upload Image": "Upload Image",
  "Device Management": "Device Management",
  "Service Life Distribution": "Service Life Distribution",
  "Operating Hours Distribution": "Operating Hours Distribution",
  "Device Status": "Device Status",
  "Duration(h)": "Duration(h)",
  "IsResolved?": "Resolved",
  "Are you sure?": "Are you sure?",
  "Are you sure unlock?": "Are you sure unlock the vehicle?",
  "Are you sure lock?": "Are you sure lock the vehicle?",
  "locked":'locked',
  "Are you sure to delete":'Are you sure to delete?',
  "Time": "Time",
  "Service life(month)": "Service life(month)",
  "h": "h",
  "The phone number has already been registered": "The phone number has already been registered",
  "Home": "Home",
  "Quantity(Liang)": "quantity",
  "soc": "soc",
  "SOC": "SOC",
  "SOC Distribution": "SOC Distribution",
  "soh": "soh",
  "SOH":"SOH",
  "staion_igbtwendu": "IGBT Temp",
  "station_24xiaoshidianliangbianhua": "24 Hours SOC",
  "station_bangdingqiye": "Bind Enterprise",
  "No": "No",
  "station_bianliuqi": "PCS",
  "station_bianyaqishuliang": "Number of Transformers",
  "station_bingwangzhuangtai": "Grid Connection",
  "station_bmsfuwei": "BMS Reset",
  "station_celueguanli": "Strategy Manage",
  "station_chakanzhandianxiangqing": "View Station Details",
  "station_chongfangdianliang": "Charge/Discharge Amount",
  "station_chongfangdianqushi": "Charge/Discharge Trend",
  "station_chongfangdianshouyi": "Charge/Discharge Revenue",
  "station_chongfangdiantongji": "Charge/Discharge Statistics",
  "station_chongfangdianzhuangtai": "Charging Status",
  "station_chuneng": "EMS",
  "station_chunengdianbiaoyougonggonglv": "EMS Meter Active Power",
  "station_chunengzhandianSOC": "Station SOC",
  "station_chushiji": "Dehumidifier",
  "station_chushuiwendu": "Outlet Temp",
  "station_conongdu": "CO Concentration",
  "station_cuneidainxinwenchafenxi": "Cell Temperature Difference Analysis",
  "station_cuneidianxinyachafenxi": "Cell Voltage Difference Analysis",
  "station_dakai": "Open",
  "station_dianbiao": "Meter",
  "station_dianchiguanlixitong": "BMS",
  "station_dianwang": "Grid",
  "station_dianwangdianbiaoyougonggonglv": "Grid Meter Active Power",
  "station_ditu": "Map",
  "station_donghuanguanlixitong": "Environmental System",
  "station_fangdiandachenglv": "Discharge Completion Rate",
  "station_fenggutaolishouyi": "Revenue",
  "station_fenzhonghuizong": "Minute Summary",
  "station_fuwushang": "Provider",
  "station_fuzai": "Load",
  "station_gonglv": "Power",
  "station_gonglüqushifenxi": "Power Trend Analysis",
  "station_gongshuiyali": "Outlet Pressure",
  "station_guanlihoutai": "O&M Management",
  "station_huanjingshidu": "Ambient Humidity",
  "station_huanjingwendu": "Ambient Temp",
  "station_huishuiwendu": "Inlet Temp",
  "station_huishuiyali": "Inlet Pressure",
  "station_jiaoqianyiri": "vs. prev. day",
  "station_jigui": "Container",
  "station_jiting": "Emergency Stop",
  "station_jitingkaiguan": "Emergency Stop Switch",
  "station_kehu": "Customer",
  "station_leijichongdianliang": "Charging Energy",
  "station_leijifangdianliang": "Discharging Energy",
  "station_menjin": "Access Control",
  "station_menjinkaiguan": "Access Control Switch",
  "station_options_ceshijieduan": "Testing Stage",
  "station_options_dagongyeyongdian": "Large Industrial Electricity",
  "station_options_yibangongshangyeyongdian": "General Commercial Electricity",
  "station_options_zhengshitouyun": "Official Commissioning",
  "station_pcsfuwei": "PCS Reset",
  "station_qianyiriwushuju": "No Data from the Previous Day",
  "station_shebeileixing": "Device Type",
  "station_shebeixinxi": "Device Information",
  "station_shengyudianliang": "Remaining Capacity",
  "station_shouyi": "Revenue",
  "station_shouyiqushi": "Revenue Trend",
  "station_shouyitongji": "Revenue Statistics",
  "station_shuijin": "Water Immersion",
  "station_shuijinkaiguan": "Water Immersion Switch",
  "station_tianjiazhandian": "Bind Enterprise",
  "station_touyunriqi": "Deploy Date",
  "station_touyunzhuangtai": "Commissioning Status",
  "station_vocnongdu": "VOC Concentration",
  "station_weihurenyuan": "Maintenance Personnel",
  "Temp": "Temp",
  "station_xiaofangyangan": "Fire Protection",
  "station_xitongxiaolv": "System Efficiency",
  "station_yanwunongdu1": "Smoke Concentration 1",
  "station_yanwunongdu2": "Smoke Concentration 2",
  "station_yelengjifuwei": "Liquid Cooler",
  "station_yongdianleixing": "Electricity Type",
  "station_yueleiji": "Monthly",
  "station_yueleijishouyi": "Monthly Revenue",
  "station_yunxingxiaolv": "Efficiency",
  "station_yunxingzhuangtai": "Operating Status",
  "station_zhandianbianhao": "Station No",
  "station_zhandiandiqu": "Region",
  "station_zhandiandizhi": "Station Address",
  "station_zhandianmingcheng": "Station Name",
  "station_zhandiantupian": "Station Image",
  "station_zhandianzuobiao": "Coordinates",
  "station_zhanshiziduan": "Display Fields",
  "station_zhengtiyunxingzhuangtai": "Overall Status",
  "station_zhuangjigonglv": "Rated Power",
  "station_zhuangjirongliang": "Rated Capacity",
  "station_zonghexiaolv": "System Efficiency",
  "station_zongji": "Total",
  "station_zongshouyi": "Total Revenue",
  "station_zuorichongdianliang": "Yesterday's Charging",
  "station_zuorifangdianliang": "Yesterday's Discharging",
  "station_zuorishouyi": "Yesterday Revenue",
  "status_chongdian": "Charging",
  "status_chongdianzhong": "Charging",
  "status_cuowu": "wrong",
  "status_daiji": "Standby",
  "status_fangdian": "Discharging",
  "status_fangdianzhong": "Discharging",
  "status_lixian": "Offline",
  "Activated":'Activated',
  "NotActivated": "Not Activated",
  "status_yichang": "Abnormal",
  "status_zaixian": "Online",
  "status_zhengchang": "Normal",
  "sub_guanliyuanshouji": "Administrator Phone",
  "sub_guanliyuanxingming": "Administrator Name",
  "sub_kaitongzizhanghuquanxian": "Sub-account Create Permission",
  "sub_options_donglidianchi": "Power Battery",
  "sub_options_gongshangyechuneng": "Industrial Energy Storage",
  "sub_sfjyktzzhqx": "Has Sub-account Create Permission",
  "sub_tianjiashijian": "Add Time",
  "sub_tips01": "Check this box to grant the account permission to create sub-accounts (No need to check when creating accounts for end users)",
  "sub_yewufanwei": "Business Scope",
  "sub_zichanghuguanli": "Sub-account",
  "sub_zichanghumingcheng": "Sub-account Name",
  "suoche": "Lock",
  "system_chuneng": "Energy Storage",
  "system_dongli": "Power Battery",
  "textarea_tips01": "Please Provide Additional Explanation for the Cause",
  "tianjia": "Add",
  "tianjiashebei_tips01": "Device cannot be empty",
  "tianjiashebei_tips02": "Device No cannot be empty",
  "tianjiashebei_tips03": "Device No must be unique",
  "tianjiashebei_tips04": "Transformer cannot be empty",
  "tianjiashebei_tips05": "Device Name cannot be empty",
  "tianjiashebei_tips06": "Vehicle Type cannot be empty",
  "tianjiashebei_tips07": "Cell Model cannot be empty",
  "tianjiashebei_tips08": "Device Name must be unique",
  "tianjiashebei_tips09": "At least one device must be retained",
  "tianjiashebei_tips10":'Battery No cannot be empty',
  "tianjiashebei_tips11":'Battery No must be unique',
  "tishi": "tips",
  "to": "to",
  "tongji": "statistics",
  "tupian": "Image",
  "tupian_tips": "Image Desc",
  "unit_danwei": "Unit",
  "unit_shi": "Hour",
  "upload_chang": "Length",
  "upload_chicunyaoqiu": "Size Requirements",
  "upload_kuan": "Width",
  "upload_shangchuanwenjian": "Upload File",
  "upload_tips001": "Size must not exceed",
  "upload_tips002": "Image Format Error, the image format should be",
  "upload_tips003": "Image Format Error, the image format should be s% or s%",
  "user_EMSkongzhiquanxian": "EMS Control Permissions",
  "user_DataRange":"Data Range",
  "placeholder_qingxuanzeshujufanwei": "Please select data range",
  'kejianzhandian':'Visible Sites',
  "qingxuanzekejianzhandian":'Please select visible sites',
  "shangjiqiye":"Parent company",
  "user_quanxian": "Permissions",
  "user_shangcidenglushujian": "Last Login Time",
  "user_xingming": "Name",
  "user_yonghuguanli": "User",
  "username": "Username",
  "vehicle_battery.realData.CU.alarmNub": "Number of Alarms",
  "vehicle_battery.realData.CU.alarmSta": "Alarm Status",
  "vehicle_battery.realData.CU.capacity": "System Capacity",
  "vehicle_battery.realData.CU.cc2Res": "CC2 Resistance Value",
  "vehicle_battery.realData.CU.cellNub": "Cell Series Count",
  "vehicle_battery.realData.CU.chargeStatus": "Charge/Discharge Status",
  "vehicle_battery.realData.CU.electricity": "System Current",
  "vehicle_battery.realData.CU.heartbeat": "Heartbeat Status",
  "vehicle_battery.realData.CU.highVolSta": "High Voltage Detection",
  "vehicle_battery.realData.CU.inputSignalSta": "Input Signal Status",
  "vehicle_battery.realData.CU.insF": "Negative Insulation",
  "vehicle_battery.realData.CU.insZ": "Positive Insulation",
  "vehicle_battery.realData.CU.latitude4g": "Latitude",
  "vehicle_battery.realData.CU.lockSta": "Lock Status",
  "vehicle_battery.realData.CU.longitude4g": "Longitude",
  "vehicle_battery.realData.CU.realySta": "Relay Status",
  "vehicle_battery.realData.CU.requestChrgC": "Requested Charging Current",
  "vehicle_battery.realData.CU.requestVol": "Requested Voltage",
  "vehicle_battery.realData.CU.signal4g": "4G Signal",
  "vehicle_battery.realData.CU.soc": "SOC",
  "vehicle_battery.realData.CU.soh": "SOH",
  "vehicle_battery.realData.CU.tempAvg": "Avg Temperature",
  "vehicle_battery.realData.CU.tempDif": "Temp Differential",
  "vehicle_battery.realData.CU.tempMax": "Max Cell Temp",
  "vehicle_battery.realData.CU.tempMaxId": "Max Cell Temp Index",
  "vehicle_battery.realData.CU.tempMin": "Min Cell Temp",
  "vehicle_battery.realData.CU.tempMinId": "Min Cell Temp Index",
  "vehicle_battery.realData.CU.tempNub": "Temp Sensor Count",
  "vehicle_battery.realData.CU.totalChargeAh": "Total Charge Energy",
  "vehicle_battery.realData.CU.totalDischargeAh": "Total Discharge Energy",
  "vehicle_battery.realData.CU.volAvg": "Avg Voltage",
  "vehicle_battery.realData.CU.volDif": "Voltage Differential",
  "vehicle_battery.realData.CU.volMax": "Max Cell Voltage",
  "vehicle_battery.realData.CU.volMaxId": "Max Cell Voltage Index",
  "vehicle_battery.realData.CU.volMin": "Min Cell Voltage",
  "vehicle_battery.realData.CU.volMinId": "Min Cell Voltage Index",
  "vehicle_battery.realData.CU.voltage": "System Voltage",
  "weixiujilu": "Maintenance Record",
  "weixiujilu_tips01": "Please Provide Additional Explanation for the Maintenance Record",
  "wuyichang": "No Abnormalities",
  "xiangqing": "Detail",
  "xinjian": "Add",
  "xinzenggongdan": "Add Work Order",
  "yibaozhang": "Reported",
  "yichangbianhao": "Anomaly No",
  "yichangjibie": "Abnormal Level",
  "yichangshijian": "Abnormal Time",
  "yichangxiangqing": "Anomaly Detail",
  "yichu": "Remove",
  "yihuifu": "Recovered",
  "yihulue": "Ignored",
  "zanwushuju": "No Data",
  "zhengxiangyougongzongdianneng": "Forward Active Energy",
  "zhongyao": "major",
  "zhuangtai": "Status",
  "zhuangtaifenbu": "Status Distribution",
  "zhuanweigongdan": "Convert to Work Order",
  "zongshizaigonglv": "Apparent Power",
  "zongwugonggonglv": "Reactive Power",
  "zongyougonggonglv": "Active Power",
  "notAccepted": 'You have not accepted the agreement',
  'login': 'Login',
  'demoLogin': 'Demo account login',
  "UnderstandAndAgree": 'Understand and agree',
  "UserAgreement": 'User Agreement',
  "toLogin": 'to register or log in',
  "SMSCode": 'SMS verification code',
  "allData":"All data",
  "OnlySelectedData":'Only selected data',
  "OrganizationName":'Organization Name',
  'operation':'Operation',
  'zhandianshitu':'Site View',
  'shebeishitu':'Device View',
  'shebeishaixuan':'Device Filter',
  'BatteryNo':'Battery No',
  'BMS型号':'BMS Model',
  'Capacity':'Capacity',
  'service_expire_time':'Expiration Date',
  //
  "激活": "Activated",
  "在线": "Onlined",
  "中文":'Chinese',
  "Metrics View":"Metrics View",
  "Relay Status":"Relay Status",
  "signal_strength":"Signal Strength",
  "所在地区":"Location",
  "Cell Series Count":"Cell Series Count",
  "电芯温感数":"Cell Temperature Sensor Count",
  "正极绝缘阻值":"Positive Insulation Resistance",
  "系统时间":"System Time",
  "负极绝缘阻值":"Negative Insulation Resistance",
  'Recovery time':'Recovery time',
  "Project name":"Project name",
"切换":'切换',
"动力电池": "Power Battery",
"Power type":'Power type',
"runTime":'Run time',
"昨日充电时长":"Yesterday's Charging",
"昨日放电时长":"Yesterday's Discharging",
"当前异常":"Current exception",
"今日新增":'Today added',
"七日新增":'Seven days added',
"实时状态":"Real-time Info",
"历史数据":"History Data",
"是否确认启用":"Are you sure to enable",
"是否确认停用":"Are you sure to discontinue",
"Deactivate":"Deactivate",
"Device status statistics":"Device status statistics",
"Charge and discharge capacity":"Charge and discharge capacity",
"Average running time":"Average running time",
"客户名称":"Customer Name",
"Expired":"Expired",
"Custom Columns": "Custom Columns",
"batch_add": "Batch Add",
"column_settings": "Column Settings",
"Select all": "Select all",
"invert_selection": "Invert Selection",
"batch_add_device": "Batch Add Device",
"step_one": "Step One",
"step_one_desc": "Complete the table content according to the prompts",
"download_template": "Download Template",
"step_two": "Step Two: Upload the Completed Template",
"drag_upload_tip": "Drag the modified template file here or click to upload",
"filter": "Filter",
"filter_by_project": "By Project",
"filter_by_customer": "By Customer",
"filter_by_group": "By Group",
"activation_status": "Activation Status",
"system_status": "System Status",
"service_status": "Service Status",
"activation_date": "Activation Date",
"last_data_time": "Last Data Time",
"alarm_count": "Alarm Count",
"total_charge_time": "Total Charge Time(h)",
"total_charge_capacity": "Total Charge Capacity(Ah)",
"total_discharge_time": "Total Discharge Time(h)",
"total_discharge_capacity": "Total Discharge Capacity(Ah)",
"last_charge_time": "Last Charge Time",
"last_discharge_time": "Last Discharge Time",
"cycle_count": "Cycle Count",
"reset": "Reset",
"charging time":'Charging Time',
"CellPackaging":'Cell Packaging',
"Discharging Time":"Discharging Time",
"BatteryNumber":'Battery Number',
"AccumulatedChargingCapacity":"Charging Capacity",
"Individual voltage":"Individual voltage",
"AccumulatedDischargingCapacity":"Discharging Capacity",
"RatedTotalVoltage":"Rated Total Voltage",
"software_version":"Software Version",
"manufacture_date":"Manufacture Date",
"rated_total_current":"Rated Total Current",
"rated_energy":"Rated Energy",
"operation_data":"Operation Data",
"device_data":"Device Data",
"statistical_analysis":"Statistical Analysis",
"charging_behavior_analysis":"Charging Behavior Analysis",
"charging_period_distribution":"Charging period distribution",
"SOC_distribution_at_the_start_of_charging":"SOC distribution at the start of charging",
"single_charge_capacity_distribution":"Single charge capacity distribution",
"InputToSearch01":'Enter device No or name to search',
'sys_voltage':'System Voltage',
'sys_currents':'System Currents',
'ins_p':'Positive Insulation',
'ins_n':'Negative Insulation',
'vol_max':'Max Cell Voltage',
'vol_min':'Min Cell Voltage',
'vol_dif':'Voltage Differential',
'temp_max':'Max Cell Temp',
'temp_min':'Min Cell Temp',
'chger_out_volt':'Charger Output Voltage',
'chger_out_curr':'Charger Output Currents',
'cc_res':'CC Resistance Value',
'cc2_res':'CC2 Resistance Value',
'cp_freq':'CP Frequency',
'cp_pwm':'CP Duty Cycle',
'Total Discharge Cap':'Total Discharge Cap',
'Total Charge Cap':'Total Charge Cap',
"voltage_details":'Voltage Details',
"temp_details":'Temp Details',
"Overall historical range analysis":"Overall historical range analysis",
"Overall historical analysis of variance":"Overall historical analysis of variance",
"charging time(h)":"charging time (h)",
'time (h)':"Time(h)",
"Charging times":"Charging times",
'Battery capacity':'Capacity(%)',
"Quantity":"Quantity",
"Charge/discharge capacity":"Charge/discharge capacity",
"Initial SOC":"Initial SOC",
"Final SOC":"Final SOC",
"This time duration":"This time duration",
"duration":"duration",
"Collection time":"Collection time",
"Temperature sensing number":"Temperature sensing number",
"System Capacity":"System Capacity",
"System SOC":"System SOC",
"System SOH":"System SOH",
"Charging and discharging status":"Charging and discharging status",
"Max voltage position":"Max voltage position",
"Min voltage position":"Min voltage position",
"Average voltage":"Average voltage",
"Max temperature position":"Max temperature position",
"Min temperature position":"Min temperature position",
"Temp difference":"Temp difference",
"Alarm status":"Alarm status",
'Number of alarms':"Number of alarms",
"Heartbeat status":'Heartbeat status',
"High voltage detection":"High voltage detection",
"CC2 resistance":"CC2 resistance",
"Charge request current":"Charge request current",
"Request voltage":"Request voltage",
"Input signal status":"Input signal status",
"4G signal":"4G signal",
"Reserved longitude":"Reserved longitude",
"Reserved latitude":"Reserved latitude",
"Lock status":"Lock status",
"Bms model":"Bms model",
"CC resistance":"CC resistance",
"CP duty cycle":"CP duty cycle",
"CP frequency":"CP frequency",
"Charging communication status":"Charging communication status",
"Charger fault status":"Charger fault status",
"Total Energy":"Total Energy",
"MOS temperature":"MOS temperature",
"Cycle times":"Cycle times",
"Balanced mode":"Balanced mode",
"Balance current":"Balance current",
"Balanced logo":"Balanced logo ",
"Fault Code":"Fault Code",
"Voltage":"Voltage",
"Device alarm":"Device alarm",
"Power battery management":"Power battery management",
"Project management":"Project management",
"Alarm Management":"Alarm Management",
"Add project":"Add project",
"Search or create a new option":"Search or create a new option",
"Create new options":"Create new options",
"Battery series count": "Battery series count",
"Battery parallel count": "Battery parallel count",
"Battery box count": "Battery box count",
"Rated power": "Rated power",
"Rated current": "Rated current",
"Rated capacity": "Rated capacity",
"Rated energy": "Rated energy",
"Initiation date": "Initiation date",
"Project description": "Description",
"Project No": "Project No",
"Main customer": "Main customer",
"Total devices": "Total devices",
"Online": "Online",
"Activated1": "Activated",
"Activated devices": "Activated devices",
"Online devices": "Online devices",
"Device model": "Device model",
"Vehicle type": "Vehicle type",
"Region": "Region",
"Total capacity": "Total capacity",
"Total power": "Total power",
"Charging duration": "Charge duration",
"Charging amount": "Charge capacity",
"Charge": "Charge",
"Discharging duration": "Discharge duration",
"Discharging amount": "Discharge capacity",
"Discharge": "Discharge",
"Alarm count": "Alarm count",
"Project detail":"Project detail",
"Belonging project": "Belonging project",
"Activation time": "Activation time",
"Only Excel files can be uploaded !": "Only Excel files can be uploaded !",
"Please upload the file first": "Please upload the file first",
"SMS verification":"SMS verification",
"Email Verification":"Email Verification",
}